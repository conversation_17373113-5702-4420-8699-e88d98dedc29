/**
 * Project Documentation Adapter
 * Preserves capabilities from permanent_work_intake.html and general project workflows
 * Focuses on wizard integration and project data population
 */

class ProjectDocumentationAdapter {
    constructor(config) {
        this.config = config;
        this.projectData = null;
        this.loadProjectData();
    }

    async loadProjectData() {
        // Load current project data from existing systems
        this.projectData = window.currentProjectData || this.getDefaultProjectData();
    }

    /**
     * Process file with focus on project integration
     */
    async processFile(fileObj, progressCallback) {
        const steps = [
            { name: 'Extracting document content', progress: 20, duration: 1500 },
            { name: 'Analyzing project relevance', progress: 40, duration: 2000 },
            { name: 'Extracting project data', progress: 60, duration: 2500 },
            { name: 'Integrating with wizard', progress: 80, duration: 1500 },
            { name: 'Preparing auto-population', progress: 100, duration: 1000 }
        ];

        let analysis = {
            type: 'project-documentation',
            extractedContent: null,
            projectRelevance: null,
            extractedProjectData: null,
            wizardIntegration: null,
            autoPopulationData: null
        };

        // Step 1: Extract document content
        progressCallback(fileObj.id, steps[0].name, steps[0].progress);
        await this.delay(steps[0].duration);
        analysis.extractedContent = await this.extractContent(fileObj);

        // Step 2: Analyze project relevance
        progressCallback(fileObj.id, steps[1].name, steps[1].progress);
        await this.delay(steps[1].duration);
        analysis.projectRelevance = await this.analyzeProjectRelevance(analysis.extractedContent);

        // Step 3: Extract project data
        progressCallback(fileObj.id, steps[2].name, steps[2].progress);
        await this.delay(steps[2].duration);
        analysis.extractedProjectData = await this.extractProjectData(analysis.extractedContent);

        // Step 4: Integrate with wizard
        progressCallback(fileObj.id, steps[3].name, steps[3].progress);
        await this.delay(steps[3].duration);
        analysis.wizardIntegration = await this.prepareWizardIntegration(analysis.extractedProjectData);

        // Step 5: Prepare auto-population
        progressCallback(fileObj.id, steps[4].name, steps[4].progress);
        await this.delay(steps[4].duration);
        analysis.autoPopulationData = await this.prepareAutoPopulation(analysis);

        return analysis;
    }

    /**
     * Extract content from document
     */
    async extractContent(fileObj) {
        return {
            text: 'Sample extracted text content...',
            metadata: {
                pages: 1,
                fileType: fileObj.type,
                extractionMethod: 'OCR'
            },
            structuredData: {
                tables: [],
                forms: [],
                lists: []
            },
            keyPhrases: [
                'project description',
                'cost estimate',
                'damage assessment',
                'scope of work'
            ]
        };
    }

    /**
     * Analyze how relevant the document is to the current project
     */
    async analyzeProjectRelevance(extractedContent) {
        return {
            relevanceScore: 0.85,
            relevanceFactors: [
                'Contains project-related keywords',
                'Includes cost information',
                'Has damage descriptions',
                'Contains location references'
            ],
            documentPurpose: 'project-support',
            suggestedUse: 'wizard-auto-population',
            confidence: 0.8
        };
    }

    /**
     * Extract project-specific data from document
     */
    async extractProjectData(extractedContent) {
        return {
            projectInfo: {
                title: 'Sample Project Title',
                location: 'Sample Location',
                description: 'Sample project description extracted from document',
                incidentDate: null,
                estimatedCost: null
            },
            applicantInfo: {
                name: null,
                type: null,
                contact: null,
                address: null
            },
            damageInfo: {
                description: 'Sample damage description',
                category: null,
                severity: 'moderate',
                affectedAreas: []
            },
            costInfo: {
                laborCosts: null,
                materialCosts: null,
                equipmentCosts: null,
                totalEstimate: null
            },
            complianceInfo: {
                environmentalReview: null,
                historicPreservation: null,
                insuranceCoverage: null
            }
        };
    }

    /**
     * Prepare data for wizard integration
     */
    async prepareWizardIntegration(extractedProjectData) {
        return {
            applicableSteps: [
                'project-information',
                'damage-assessment',
                'cost-estimation',
                'insurance-coordination'
            ],
            fieldMappings: {
                'projectTitle': extractedProjectData.projectInfo?.title,
                'projectLocation': extractedProjectData.projectInfo?.location,
                'damageDescription': extractedProjectData.damageInfo?.description,
                'estimatedCost': extractedProjectData.costInfo?.totalEstimate
            },
            validationStatus: {
                'projectTitle': extractedProjectData.projectInfo?.title ? 'valid' : 'missing',
                'projectLocation': extractedProjectData.projectInfo?.location ? 'valid' : 'missing',
                'damageDescription': extractedProjectData.damageInfo?.description ? 'valid' : 'missing'
            },
            recommendations: [
                'Auto-populate project title field',
                'Pre-fill damage description',
                'Suggest cost estimate values'
            ]
        };
    }

    /**
     * Prepare auto-population data for forms
     */
    async prepareAutoPopulation(analysis) {
        return {
            formFields: {
                'applicantName': analysis.extractedProjectData?.applicantInfo?.name,
                'projectTitle': analysis.extractedProjectData?.projectInfo?.title,
                'projectLocation': analysis.extractedProjectData?.projectInfo?.location,
                'incidentDate': analysis.extractedProjectData?.projectInfo?.incidentDate,
                'damageDescription': analysis.extractedProjectData?.damageInfo?.description,
                'estimatedCost': analysis.extractedProjectData?.costInfo?.totalEstimate
            },
            confidence: {
                'applicantName': 0.6,
                'projectTitle': 0.8,
                'projectLocation': 0.7,
                'incidentDate': 0.5,
                'damageDescription': 0.9,
                'estimatedCost': 0.6
            },
            suggestions: [
                'Review auto-populated fields for accuracy',
                'Verify extracted cost estimates',
                'Confirm project location details'
            ]
        };
    }

    /**
     * Render project documentation analysis results
     */
    renderResults(file) {
        if (!file.analysis || file.analysis.type !== 'project-documentation') return '';

        const analysis = file.analysis;
        
        return `
            <div class="project-analysis-results">
                <div class="analysis-header">
                    <h5>📄 Project Documentation Analysis</h5>
                    <div class="relevance-score">
                        Relevance: ${((analysis.projectRelevance?.relevanceScore || 0) * 100).toFixed(1)}%
                    </div>
                </div>

                <!-- Project Relevance -->
                <div class="analysis-section">
                    <h6>🎯 Project Relevance</h6>
                    <div class="relevance-details">
                        <div class="detail-item">
                            <strong>Document Purpose:</strong> ${analysis.projectRelevance?.documentPurpose || 'Unknown'}
                        </div>
                        <div class="detail-item">
                            <strong>Suggested Use:</strong> ${analysis.projectRelevance?.suggestedUse || 'General reference'}
                        </div>
                        <div class="detail-item">
                            <strong>Confidence:</strong> ${((analysis.projectRelevance?.confidence || 0) * 100).toFixed(1)}%
                        </div>
                    </div>
                    <div class="relevance-factors">
                        <strong>Relevance Factors:</strong>
                        <ul>
                            ${analysis.projectRelevance?.relevanceFactors?.map(factor => `<li>${factor}</li>`).join('') || '<li>No specific factors identified</li>'}
                        </ul>
                    </div>
                </div>

                <!-- Extracted Project Data -->
                <div class="analysis-section">
                    <h6>📊 Extracted Project Data</h6>
                    <div class="project-data-grid">
                        <div class="data-category">
                            <strong>Project Information:</strong>
                            <ul>
                                <li><strong>Title:</strong> ${analysis.extractedProjectData?.projectInfo?.title || 'Not found'}</li>
                                <li><strong>Location:</strong> ${analysis.extractedProjectData?.projectInfo?.location || 'Not found'}</li>
                                <li><strong>Description:</strong> ${analysis.extractedProjectData?.projectInfo?.description || 'Not found'}</li>
                            </ul>
                        </div>
                        <div class="data-category">
                            <strong>Damage Information:</strong>
                            <ul>
                                <li><strong>Description:</strong> ${analysis.extractedProjectData?.damageInfo?.description || 'Not found'}</li>
                                <li><strong>Severity:</strong> ${analysis.extractedProjectData?.damageInfo?.severity || 'Not assessed'}</li>
                                <li><strong>Category:</strong> ${analysis.extractedProjectData?.damageInfo?.category || 'Not specified'}</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Wizard Integration -->
                <div class="analysis-section">
                    <h6>🧙‍♂️ Wizard Integration</h6>
                    <div class="wizard-integration">
                        <div class="applicable-steps">
                            <strong>Applicable Steps:</strong>
                            <div class="steps-list">
                                ${analysis.wizardIntegration?.applicableSteps?.map(step => `
                                    <span class="step-tag">${step.replace('-', ' ')}</span>
                                `).join('') || '<span class="step-tag">No specific steps identified</span>'}
                            </div>
                        </div>
                        <div class="field-mappings">
                            <strong>Field Mappings:</strong>
                            <div class="mappings-grid">
                                ${Object.entries(analysis.wizardIntegration?.fieldMappings || {}).map(([field, value]) => `
                                    <div class="mapping-item">
                                        <span class="field-name">${field}:</span>
                                        <span class="field-value">${value || 'Not mapped'}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto-Population Data -->
                <div class="analysis-section">
                    <h6>🔄 Auto-Population Preview</h6>
                    <div class="auto-population">
                        ${Object.entries(analysis.autoPopulationData?.formFields || {}).map(([field, value]) => {
                            const confidence = analysis.autoPopulationData?.confidence?.[field] || 0;
                            const confidenceClass = confidence > 0.8 ? 'high' : confidence > 0.6 ? 'medium' : 'low';
                            return `
                                <div class="population-item">
                                    <div class="field-info">
                                        <span class="field-label">${field.replace(/([A-Z])/g, ' $1').toLowerCase()}:</span>
                                        <span class="confidence-badge ${confidenceClass}">${(confidence * 100).toFixed(0)}%</span>
                                    </div>
                                    <div class="field-preview">${value || 'No data extracted'}</div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="analysis-section">
                    <h6>💡 Recommendations</h6>
                    <div class="recommendations">
                        <ul>
                            ${analysis.wizardIntegration?.recommendations?.map(rec => `<li>${rec}</li>`).join('') || '<li>No specific recommendations</li>'}
                        </ul>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="projectAdapter.autoPopulateWizard('${file.id}')">
                        🔄 Auto-Populate Wizard
                    </button>
                    <button class="btn btn-secondary" onclick="projectAdapter.previewPopulation('${file.id}')">
                        👁️ Preview Population
                    </button>
                    <button class="btn btn-success" onclick="projectAdapter.exportProjectData('${file.id}')">
                        📤 Export Project Data
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Show summary results for project documentation
     */
    showSummaryResults(files) {
        const projectFiles = files.filter(f => f.analysis?.type === 'project-documentation');
        if (projectFiles.length === 0) return;

        const resultsContainer = document.getElementById('results-container');
        if (!resultsContainer) return;

        const avgRelevance = projectFiles.reduce((sum, f) => sum + (f.analysis.projectRelevance?.relevanceScore || 0), 0) / projectFiles.length;
        const populatableFields = new Set();
        
        projectFiles.forEach(f => {
            Object.keys(f.analysis.autoPopulationData?.formFields || {}).forEach(field => {
                if (f.analysis.autoPopulationData.formFields[field]) {
                    populatableFields.add(field);
                }
            });
        });

        resultsContainer.innerHTML = `
            <div class="project-summary-results">
                <h5>📄 Project Documentation Summary</h5>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-value">${projectFiles.length}</div>
                        <div class="stat-label">Documents Processed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${(avgRelevance * 100).toFixed(1)}%</div>
                        <div class="stat-label">Average Relevance</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${populatableFields.size}</div>
                        <div class="stat-label">Populatable Fields</div>
                    </div>
                </div>
                
                <div class="populatable-fields">
                    <h6>🔄 Available Auto-Population Fields</h6>
                    <div class="fields-list">
                        ${Array.from(populatableFields).map(field => `
                            <span class="field-tag">${field.replace(/([A-Z])/g, ' $1').toLowerCase()}</span>
                        `).join('') || '<span class="field-tag">No fields available</span>'}
                    </div>
                </div>
                
                <div class="summary-actions">
                    <button class="btn btn-primary" onclick="projectAdapter.autoPopulateAllFields()">
                        🔄 Auto-Populate All Fields
                    </button>
                    <button class="btn btn-secondary" onclick="projectAdapter.generateProjectSummary()">
                        📊 Generate Project Summary
                    </button>
                </div>
            </div>
        `;
    }

    // Action methods
    autoPopulateWizard(fileId) {
        alert('🔄 Auto-Populating Wizard\n\nPopulating wizard fields with extracted data...');
    }

    previewPopulation(fileId) {
        alert('👁️ Previewing Population\n\nShowing preview of auto-populated fields...');
    }

    exportProjectData(fileId) {
        alert('📤 Exporting Project Data\n\nExporting extracted project data in JSON format...');
    }

    autoPopulateAllFields() {
        alert('🔄 Auto-Populating All Fields\n\nPopulating all available fields from processed documents...');
    }

    generateProjectSummary() {
        alert('📊 Generating Project Summary\n\nCreating comprehensive project summary from all documents...');
    }

    // Utility methods
    getDefaultProjectData() {
        return {
            id: null,
            title: null,
            location: null,
            category: null,
            status: 'draft'
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Make available globally
window.ProjectDocumentationAdapter = ProjectDocumentationAdapter;
window.projectAdapter = new ProjectDocumentationAdapter();

export { ProjectDocumentationAdapter };
