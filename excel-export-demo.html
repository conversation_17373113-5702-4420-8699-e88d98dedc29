<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel Export Demo - Real FEMA Workbook Generation</title>
    <link rel="stylesheet" href="ui/nav.css">
    <style>
        body {
            font-family: 'Lato', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .workbook-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .sheet-preview {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            background: #f9fafb;
        }
        
        .sheet-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3b82f6;
        }
        
        .sheet-content {
            font-size: 0.9em;
            color: #6b7280;
        }
        
        .sample-data {
            background: #f3f4f6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 0.85em;
        }
        
        .formula-example {
            background: #ecfdf5;
            border: 1px solid #10b981;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            color: #047857;
        }
        
        .export-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .control-group {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
        }
        
        .control-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 5px;
        }
        
        .btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #059669;
        }
        
        .btn-success:hover {
            background: #047857;
        }
        
        .btn-secondary {
            background: #6b7280;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }
        
        .status-success {
            background: #ecfdf5;
            border: 1px solid #10b981;
            color: #047857;
        }
        
        .status-error {
            background: #fef2f2;
            border: 1px solid #ef4444;
            color: #dc2626;
        }
        
        .status-info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #059669;
        }
        
        .feature-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            color: #6b7280;
            font-size: 0.9em;
            margin-bottom: 5px;
            padding-left: 16px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="demo-header">
            <h1>📊 Real Excel Export Demo</h1>
            <p>Generate actual FEMA Project Workbooks with formulas, formatting, and cross-sheet calculations</p>
        </div>

        <!-- Export Controls -->
        <div class="demo-section">
            <h2>🚀 Export Controls</h2>
            <div class="export-controls">
                <div class="control-group">
                    <div class="control-label">Sample Data Export</div>
                    <p>Generate a workbook with sample project data to test the Excel export functionality.</p>
                    <button class="btn btn-success" onclick="exportSampleWorkbook()">
                        📊 Export Sample Workbook
                    </button>
                </div>
                
                <div class="control-group">
                    <div class="control-label">Empty Template Export</div>
                    <p>Generate an empty FEMA workbook template ready for manual data entry.</p>
                    <button class="btn btn-secondary" onclick="exportEmptyTemplate()">
                        📋 Export Empty Template
                    </button>
                </div>
                
                <div class="control-group">
                    <div class="control-label">Custom Project Export</div>
                    <p>Export workbook with custom project information entered below.</p>
                    <input type="text" id="projectTitle" placeholder="Project Title" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                    <input type="text" id="applicantName" placeholder="Applicant Name" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                    <button class="btn btn-primary" onclick="exportCustomWorkbook()">
                        🔧 Export Custom Workbook
                    </button>
                </div>
            </div>
            
            <div id="statusMessage" class="status-message"></div>
        </div>

        <!-- Workbook Preview -->
        <div class="demo-section">
            <h2>📋 Workbook Structure Preview</h2>
            <p>The generated Excel workbook includes these sheets with real formulas:</p>
            
            <div class="workbook-preview">
                <div class="sheet-preview">
                    <div class="sheet-title">📋 Project Information</div>
                    <div class="sheet-content">
                        Auto-populated project details including applicant information, project title, category, and damage description.
                        <div class="sample-data">
                            Applicant Name: Sample City<br>
                            Project Title: Storm Damage Repair<br>
                            Category: Category C - Roads<br>
                            DR Number: DR-4XXX-XX
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">👷 Labor Costs</div>
                    <div class="sheet-content">
                        Labor cost breakdown with automatic calculations.
                        <div class="formula-example">
                            Formula: =B2*C2 (Hours × Rate = Total)
                        </div>
                        <div class="sample-data">
                            Skilled Labor: 120 hrs × $65 = $7,800<br>
                            General Labor: 80 hrs × $45 = $3,600<br>
                            TOTAL: =SUM(D2:D4)
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">🚜 Equipment Costs</div>
                    <div class="sheet-content">
                        Equipment rental and usage costs with formulas.
                        <div class="formula-example">
                            Formula: =B2*C2 (Hours × Rate = Total)
                        </div>
                        <div class="sample-data">
                            Excavator: 16 hrs × $125 = $2,000<br>
                            Crane: 8 hrs × $200 = $1,600<br>
                            TOTAL: =SUM(D2:D4)
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">🧱 Materials Costs</div>
                    <div class="sheet-content">
                        Materials with quantity and unit cost calculations.
                        <div class="formula-example">
                            Formula: =B2*C2 (Quantity × Unit Cost = Total)
                        </div>
                        <div class="sample-data">
                            Concrete: 50 CY × $150 = $7,500<br>
                            Steel: 2000 LB × $1.20 = $2,400<br>
                            TOTAL: =SUM(D2:D4)
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">📄 Contracts Other</div>
                    <div class="sheet-content">
                        Contract and miscellaneous costs.
                        <div class="sample-data">
                            Engineering Services: $5,000<br>
                            Permits & Fees: $1,200<br>
                            TOTAL: =SUM(B2:B4)
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">💰 Insurance DOB</div>
                    <div class="sheet-content">
                        Insurance and duplication of benefits calculations.
                        <div class="formula-example">
                            Total DOB: =B6+B7+B10+B11+B12
                        </div>
                        <div class="sample-data">
                            Insurance Settlement: $15,000<br>
                            SBA Loan: $0<br>
                            Other Federal Aid: $0<br>
                            TOTAL DOB: =SUM(...)
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">📊 Summary Totals</div>
                    <div class="sheet-content">
                        Master summary with cross-sheet formulas.
                        <div class="formula-example">
                            Net Eligible: =Subtotal - DOB<br>
                            Federal Share: =Net Eligible * 0.75
                        </div>
                        <div class="sample-data">
                            Subtotal: ='Labor Costs'!D5 + ...<br>
                            Less DOB: ='Insurance DOB'!B14<br>
                            NET ELIGIBLE: =B9-B11<br>
                            Federal Share (75%): =B13*0.75
                        </div>
                    </div>
                </div>
                
                <div class="sheet-preview">
                    <div class="sheet-title">📈 BCA Analysis</div>
                    <div class="sheet-content">
                        Benefit-Cost Analysis for projects over $1M.
                        <div class="formula-example">
                            BCR: =Present Value Benefits / Project Cost
                        </div>
                        <div class="sample-data">
                            Project Cost: $1,500,000<br>
                            Annual Benefits: $200,000<br>
                            BCR: =B8/B3<br>
                            Result: =IF(B9>=1,"FAVORABLE","UNFAVORABLE")
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="demo-section">
            <h2>⚡ Excel Export Features</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-title">🧮 Real Excel Formulas</div>
                    <ul class="feature-list">
                        <li>Automatic calculations (Qty × Rate = Total)</li>
                        <li>Cross-sheet references</li>
                        <li>SUM, IF, and complex formulas</li>
                        <li>Currency formatting</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">📋 FEMA Compliance</div>
                    <ul class="feature-list">
                        <li>Official FEMA Form 90-91 structure</li>
                        <li>Required cost categories</li>
                        <li>DOB calculations</li>
                        <li>Federal/Applicant share formulas</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">🎨 Professional Formatting</div>
                    <ul class="feature-list">
                        <li>Currency formatting ($#,##0.00)</li>
                        <li>Column width optimization</li>
                        <li>Header styling</li>
                        <li>Conditional formatting</li>
                    </ul>
                </div>
                
                <div class="feature-item">
                    <div class="feature-title">🔄 Data Integration</div>
                    <ul class="feature-list">
                        <li>Auto-population from HTML forms</li>
                        <li>Document analysis integration</li>
                        <li>Project data preservation</li>
                        <li>Wizard data mapping</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Excel Generator -->
    <script src="components/excel-workbook-generator.js"></script>
    
    <script>
        // Sample project data for demonstration
        const sampleProjectData = {
            projectInfo: {
                applicantName: 'Sample City Government',
                projectTitle: 'Hurricane Storm Damage Repair - Main Street Bridge',
                category: 'Category C - Roads and Bridges',
                papagVersion: 'PAPAG v5.0',
                drNumber: 'DR-4XXX-XX',
                incidentDate: '2024-08-15',
                projectLocation: '123 Main Street, Sample City, ST 12345',
                damageDescription: 'Bridge deck damaged by hurricane winds and debris. Structural assessment shows need for deck replacement and railing repair.',
                insuranceCoverage: 'Municipal Insurance Coverage',
                settlementAmount: '$15,000'
            },
            laborCosts: [
                { description: 'Skilled Construction Workers', hours: 120, rate: 65, notes: 'Prevailing wage rates' },
                { description: 'General Laborers', hours: 80, rate: 45, notes: 'Site preparation and cleanup' },
                { description: 'Project Supervision', hours: 20, rate: 85, notes: 'Engineering oversight' }
            ],
            equipmentCosts: [
                { description: 'Excavator Rental', hours: 16, rate: 125, notes: 'Debris removal' },
                { description: 'Crane Rental', hours: 8, rate: 200, notes: 'Bridge deck installation' },
                { description: 'Small Tools Allowance', hours: 1, rate: 500, notes: 'Miscellaneous tools' }
            ],
            materialsCosts: [
                { description: 'Concrete (Bridge Deck)', quantity: 50, unitCost: 150, unit: 'CY', notes: 'High-strength concrete' },
                { description: 'Reinforcing Steel', quantity: 2000, unitCost: 1.2, unit: 'LB', notes: 'Grade 60 rebar' },
                { description: 'Lumber (Forms)', quantity: 5000, unitCost: 0.8, unit: 'BF', notes: 'Temporary formwork' }
            ],
            contractsCosts: [
                { description: 'Engineering Services', amount: 5000, notes: 'Structural design and inspection' },
                { description: 'Permits and Fees', amount: 1200, notes: 'Municipal permits' },
                { description: 'Environmental Compliance', amount: 800, notes: 'Environmental review' }
            ],
            insuranceDOB: {
                coverage: 'Municipal General Liability',
                settlementAmount: 15000,
                deductible: 2500,
                sbaLoan: 0,
                otherFederalAid: 0,
                stateLocalAid: 0
            }
        };

        async function exportSampleWorkbook() {
            showStatus('Generating sample workbook with realistic project data...', 'info');
            
            try {
                const result = await window.excelGenerator.generateWorkbook(
                    sampleProjectData, 
                    'FEMA_Sample_Project_Workbook.xlsx'
                );
                showStatus(`✅ Sample workbook generated successfully! File: ${result.filename}`, 'success');
            } catch (error) {
                showStatus(`❌ Error generating sample workbook: ${error.message}`, 'error');
            }
        }

        async function exportEmptyTemplate() {
            showStatus('Generating empty FEMA workbook template...', 'info');
            
            try {
                const emptyData = {
                    projectInfo: {},
                    laborCosts: [],
                    equipmentCosts: [],
                    materialsCosts: [],
                    contractsCosts: [],
                    insuranceDOB: {}
                };
                
                const result = await window.excelGenerator.generateWorkbook(
                    emptyData, 
                    'FEMA_Empty_Template.xlsx'
                );
                showStatus(`✅ Empty template generated successfully! File: ${result.filename}`, 'success');
            } catch (error) {
                showStatus(`❌ Error generating template: ${error.message}`, 'error');
            }
        }

        async function exportCustomWorkbook() {
            const projectTitle = document.getElementById('projectTitle').value;
            const applicantName = document.getElementById('applicantName').value;
            
            if (!projectTitle || !applicantName) {
                showStatus('❌ Please enter both project title and applicant name', 'error');
                return;
            }
            
            showStatus('Generating custom workbook with your project data...', 'info');
            
            try {
                const customData = {
                    ...sampleProjectData,
                    projectInfo: {
                        ...sampleProjectData.projectInfo,
                        projectTitle: projectTitle,
                        applicantName: applicantName
                    }
                };
                
                const filename = `FEMA_${projectTitle.replace(/[^a-zA-Z0-9]/g, '_')}_Workbook.xlsx`;
                const result = await window.excelGenerator.generateWorkbook(customData, filename);
                showStatus(`✅ Custom workbook generated successfully! File: ${result.filename}`, 'success');
            } catch (error) {
                showStatus(`❌ Error generating custom workbook: ${error.message}`, 'error');
            }
        }

        function showStatus(message, type) {
            const statusElement = document.getElementById('statusMessage');
            statusElement.textContent = message;
            statusElement.className = `status-message status-${type}`;
            statusElement.style.display = 'block';
            
            // Auto-hide info messages after 5 seconds
            if (type === 'info') {
                setTimeout(() => {
                    statusElement.style.display = 'none';
                }, 5000);
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('Excel export system ready! Click any button above to generate a workbook.', 'info');
        });
    </script>
</body>
</html>
