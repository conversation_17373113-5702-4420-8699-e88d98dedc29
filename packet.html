<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FEMA PA Applicant Packet</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <!-- <link rel="stylesheet" href="ui/sidewalk.css"> QUARANTINED: moved to legacy/sidewalk/ -->
    <link rel="stylesheet" href="print.css" media="print">

    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <style>
        body { 
            font-family: system-ui, sans-serif; 
            margin: 20px; 
            max-width: 8.5in; 
            margin: 0 auto; 
            padding: 20px;
            background: #fff;
        }
        .project-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #000;
        }
        .project-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #000;
        }
        .project-meta {
            font-size: 14px;
            color: #666;
            margin: 5px 0;
        }
        .section {
            margin: 30px 0;
            page-break-inside: avoid;
        }
        .section h2 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #000;
            border-bottom: 2px solid #ccc;
            padding-bottom: 5px;
        }
        .section h3 {
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #333;
        }
        .cost-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
        }
        .cost-table th, .cost-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        .cost-table th {
            background: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        .cost-table .total-row {
            background: #e0e0e0;
            font-weight: bold;
        }
        .cost-sources {
            background: #f8f8f8;
            border: 1px solid #ccc;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .signature-block {
            margin-top: 40px;
            border-top: 2px solid #000;
            padding-top: 20px;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            width: 300px;
            height: 40px;
            margin: 20px 0 5px 0;
        }
        .no-print {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin: 20px 0;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        .page-break { page-break-before: always; }
        .compliance-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #28a745;
            background: #f8f9fa;
        }
        .compliance-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .compliance-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
<div id="universal-nav"></div>

<main id="main" class="container">
    <button class="no-print" onclick="window.print()">📄 Download Complete Applicant Packet PDF</button>
    
    <div class="project-header">
        <div class="project-title" id="packet-title">FEMA PA Applicant Packet</div>
        <div class="project-meta" id="packet-meta">Generated: <span id="generation-date"></span></div>
        <div class="project-meta" id="project-details"></div>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div id="executive-summary">
            <p>This comprehensive applicant packet contains all required documentation for FEMA Public Assistance (PA) funding consideration. The project has been analyzed using industry-standard cost databases and compliance frameworks to ensure accuracy and regulatory adherence.</p>
        </div>
    </div>

    <div class="section page-break">
        <h2>Project Overview</h2>
        <div id="project-overview"></div>
    </div>

    <div class="section page-break">
        <h2>CBCS Technical Justification</h2>
        <div id="cbcs-justification"></div>
    </div>

    <div class="section page-break">
        <h2>Cost Analysis & Worksheet</h2>
        <div id="cost-analysis"></div>
        
        <h3>Cost Sources & Provenance</h3>
        <div id="cost-sources-detail" class="cost-sources"></div>
        
        <h3>Regional & Edition Factors</h3>
        <div id="cost-factors"></div>
    </div>

    <div class="section page-break">
        <h2>Compliance Analysis</h2>
        <div id="compliance-analysis"></div>
        
        <h3>Environmental & Historic Preservation (EHP)</h3>
        <div id="ehp-compliance"></div>
        
        <h3>Mitigation Requirements</h3>
        <div id="mitigation-compliance"></div>
        
        <h3>Procurement Compliance</h3>
        <div id="procurement-compliance"></div>
    </div>

    <div class="section page-break">
        <h2>Supporting Documentation</h2>
        <div id="supporting-docs"></div>
        
        <h3>Attachments Manifest</h3>
        <div id="attachments-manifest"></div>
    </div>

    <div class="section">
        <h2>Certification & Signatures</h2>
        <p>I certify that the information provided in this application is true and correct to the best of my knowledge. I understand that any false statements may result in denial of assistance or recovery of funds already provided.</p>
        
        <div class="signature-block">
            <div><strong>Applicant Representative:</strong></div>
            <div class="signature-line"></div>
            <div>Signature</div>
            <br>
            <div class="signature-line"></div>
            <div>Print Name & Title</div>
            <br>
            <div class="signature-line"></div>
            <div>Date</div>
        </div>
    </div>

    <div class="print-footer">
        ComplianceMax FEMA PA System — Page <span class="pageNumber"></span>
    </div>

    <script src="cmx.js"></script>
    <script>
        // Auto-generate PDF packet content
        (function(){
            const urlParams = new URLSearchParams(window.location.search);
            const projectId = urlParams.get('project') || localStorage.getItem('cmx:packetProjectId');
            
            if (!projectId) {
                document.body.innerHTML = '<h1>Error: No project specified</h1><p>Please access this page from the dashboard.</p>';
                return;
            }

            const project = cmx.getProject(projectId);
            if (!project) {
                document.body.innerHTML = '<h1>Error: Project not found</h1><p>The specified project could not be loaded.</p>';
                return;
            }

            // Set generation date
            document.getElementById('generation-date').textContent = new Date().toLocaleDateString();
            
            // Update title and meta
            document.getElementById('packet-title').textContent = `FEMA PA Applicant Packet: ${project.title || 'Untitled Project'}`;
            document.getElementById('project-details').innerHTML = `
                <strong>Project ID:</strong> ${projectId} | 
                <strong>Category:</strong> ${project.category || 'Not specified'} | 
                <strong>Type:</strong> ${project.type || 'Not specified'}
            `;

            // Project Overview
            document.getElementById('project-overview').innerHTML = `
                <p><strong>Project Title:</strong> ${project.title || 'Untitled Project'}</p>
                <p><strong>Location:</strong> ${project.location || 'Not specified'}</p>
                <p><strong>Category:</strong> ${project.category || 'Not specified'}</p>
                <p><strong>Work Type:</strong> ${project.type || 'Not specified'}</p>
                <p><strong>Estimated Cost:</strong> ${project.estimatedCost ? '$' + Number(project.estimatedCost).toLocaleString() : 'Not calculated'}</p>
                <p><strong>Description:</strong> ${project.description || 'No description provided'}</p>
            `;

            // CBCS Justification
            const cbcsContent = project.cbcs ? `
                <h3>Selected CBCS Codes</h3>
                <ul>
                    ${(project.cbcs.selected || []).map(code => `<li>${typeof code === 'string' ? code : code.code}</li>`).join('')}
                </ul>
                <h3>Technical Justification</h3>
                <p>${project.cbcs.justification || 'No justification provided yet.'}</p>
            ` : '<p>No CBCS analysis available.</p>';
            document.getElementById('cbcs-justification').innerHTML = cbcsContent;

            // Cost Analysis (placeholder - would integrate with worksheet data)
            document.getElementById('cost-analysis').innerHTML = `
                <p>Detailed cost analysis based on industry-standard databases and regional factors.</p>
                <p><em>Note: Complete cost breakdown available in worksheet section of this system.</em></p>
            `;

            // Cost Sources
            const costSources = project.costSources || {};
            const costSourcesHtml = Object.keys(costSources).length > 0 ? 
                Object.entries(costSources).map(([source, data]) => 
                    `<div><strong>${source}:</strong> ${data.lines} line items, total ${data.total.toLocaleString('en-US', {style: 'currency', currency: 'USD'})}${data.refs && data.refs.length ? ` (References: ${data.refs.join(', ')})` : ''}</div>`
                ).join('') : 
                '<p>Cost source tracking will be populated from worksheet data.</p>';
            document.getElementById('cost-sources-detail').innerHTML = costSourcesHtml;

            // Cost Factors
            document.getElementById('cost-factors').innerHTML = `
                <p><strong>Regional Factor:</strong> ${project.region || 'default'}</p>
                <p><strong>Cost Database Editions:</strong> RSMeans 2025, FEMA Equipment Rates 2024</p>
            `;

            // Compliance Analysis
            document.getElementById('compliance-analysis').innerHTML = `
                <p>This project has been analyzed for compliance with all applicable federal regulations including:</p>
                <ul>
                    <li>2 CFR 200 - Uniform Administrative Requirements</li>
                    <li>44 CFR 206 - Federal Disaster Assistance</li>
                    <li>Environmental & Historic Preservation requirements</li>
                    <li>Mitigation and hazard reduction standards</li>
                    <li>Federal procurement regulations</li>
                </ul>
            `;

            // Supporting Documentation
            document.getElementById('supporting-docs').innerHTML = `
                <p>All supporting documentation has been compiled and is available for FEMA review:</p>
                <ul>
                    <li>Project photographs and damage assessment</li>
                    <li>Cost estimates and vendor quotes</li>
                    <li>Technical specifications and drawings</li>
                    <li>Environmental compliance documentation</li>
                    <li>Insurance and other funding source information</li>
                </ul>
            `;

            // Attachments Manifest
            const uploads = project.uploads || [];
            const manifestHtml = uploads.length > 0 ? 
                `<table class="cost-table">
                    <thead>
                        <tr><th>Document Name</th><th>Type</th><th>Size</th><th>Upload Date</th></tr>
                    </thead>
                    <tbody>
                        ${uploads.map(upload => `
                            <tr>
                                <td>${upload.name}</td>
                                <td>${upload.type}</td>
                                <td>${upload.size}</td>
                                <td>${new Date(upload.ts).toLocaleDateString()}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>` : 
                '<p>No attachments uploaded yet.</p>';
            document.getElementById('attachments-manifest').innerHTML = manifestHtml;

            // Log the packet generation
            cmx.activity(projectId, 'Applicant packet PDF generated');
        })();
    </script>
</main>

<div id="cmx-footer"></div>

</body>
</html>
