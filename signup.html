<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - ComplianceMax</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
            min-height: 100vh;
        }

        .signup-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .signup-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            margin: 20px;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: #3b82f6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
        }

        .signup-title {
            font-size: 28px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .signup-subtitle {
            color: #6b7280;
            margin-bottom: 32px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .subscription-section {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 24px;
        }

        .subscription-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }

        .subscription-options {
            display: grid;
            gap: 12px;
        }

        .subscription-option {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s;
            background: white;
        }

        .subscription-option:hover {
            border-color: #3b82f6;
        }

        .subscription-option.selected {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .subscription-option input[type="radio"] {
            display: none;
        }

        .subscription-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }

        .subscription-name {
            font-weight: 600;
            color: #1f2937;
        }

        .subscription-price {
            font-weight: bold;
            color: #3b82f6;
            margin-left: auto;
        }

        .subscription-features {
            font-size: 14px;
            color: #6b7280;
            line-height: 1.4;
        }

        .recommended-badge {
            background: #3b82f6;
            color: white;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            margin-left: 8px;
        }

        .signup-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .signup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .signup-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .signin-link {
            text-align: center;
            color: #6b7280;
        }

        .signin-link a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 600;
        }

        .signin-link a:hover {
            text-decoration: underline;
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            transition: background-color 0.3s;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.2);
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 640px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="universal-nav" class="site-nav"></div>
    <main id="main" class="container">
        <div class="signup-wrapper">
            <div class="signup-container">
        <a href="landing_page.html" class="back-link">
            ← Back to Home
        </a>
        <div class="logo-section">
            <div class="logo">
                <div class="logo-icon">CM</div>
                <div class="logo-text">ComplianceMax</div>
            </div>
            <h1 class="signup-title">Create Your Account</h1>
            <p class="signup-subtitle">Join thousands of professionals streamlining FEMA compliance</p>
        </div>

        <div id="errorMessage" class="error-message"></div>
        <div id="successMessage" class="success-message"></div>

        <form id="signupForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName" class="form-label">First Name</label>
                    <input type="text" id="firstName" name="firstName" class="form-input" required placeholder="John">
                </div>
                <div class="form-group">
                    <label for="lastName" class="form-label">Last Name</label>
                    <input type="text" id="lastName" name="lastName" class="form-input" required placeholder="Smith">
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input type="email" id="email" name="email" class="form-input" required placeholder="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="organization" class="form-label">Organization</label>
                <input type="text" id="organization" name="organization" class="form-input" required placeholder="City of Springfield">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" id="password" name="password" class="form-input" required placeholder="Create password">
                </div>
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" class="form-input" required placeholder="Confirm password">
                </div>
            </div>

            <div class="subscription-section">
                <h3 class="subscription-title">Choose Your Plan</h3>
                <div class="subscription-options">
                    <label class="subscription-option" for="basic">
                        <input type="radio" id="basic" name="subscription" value="basic">
                        <div class="subscription-header">
                            <span class="subscription-name">Basic Compliance</span>
                            <span class="subscription-price">$199/month</span>
                        </div>
                        <div class="subscription-features">Essential tools for straightforward projects</div>
                    </label>

                    <label class="subscription-option selected" for="professional">
                        <input type="radio" id="professional" name="subscription" value="professional" checked>
                        <div class="subscription-header">
                            <span class="subscription-name">Professional</span>
                            <span class="recommended-badge">Recommended</span>
                            <span class="subscription-price">$499/month</span>
                        </div>
                        <div class="subscription-features">Advanced automation for complex projects</div>
                    </label>

                    <label class="subscription-option" for="enterprise">
                        <input type="radio" id="enterprise" name="subscription" value="enterprise">
                        <div class="subscription-header">
                            <span class="subscription-name">Enterprise</span>
                            <span class="subscription-price">$999/month</span>
                        </div>
                        <div class="subscription-features">Complete solution for large organizations</div>
                    </label>
                </div>
            </div>

            <button type="submit" id="signupBtn" class="signup-btn">
                <span id="signupText">Create Account & Subscribe</span>
                <span id="signupLoading" class="loading" style="display: none;"></span>
            </button>
        </form>

        <div class="signin-link">
            Already have an account? <a href="signin.html">Sign in here</a>
        </div>
            </div>
        </div>
    </main>
    <div id="cmx-footer"></div>

    <script>
        // Handle subscription option selection
        document.querySelectorAll('.subscription-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.subscription-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type="radio"]').checked = true;
            });
        });

        // Handle form submission
        document.getElementById('signupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            const signupBtn = document.getElementById('signupBtn');
            const signupText = document.getElementById('signupText');
            const signupLoading = document.getElementById('signupLoading');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');

            // Hide previous messages
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            // Validate passwords match
            if (password !== confirmPassword) {
                errorMessage.textContent = 'Passwords do not match.';
                errorMessage.style.display = 'block';
                return;
            }

            // Show loading state
            signupBtn.disabled = true;
            signupText.style.display = 'none';
            signupLoading.style.display = 'inline-block';

            try {
                // Simulate account creation (replace with real API call)
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Store user data
                const userData = {
                    firstName: formData.get('firstName'),
                    lastName: formData.get('lastName'),
                    email: formData.get('email'),
                    organization: formData.get('organization'),
                    subscription: formData.get('subscription')
                };

                localStorage.setItem('isAuthenticated', 'true');
                localStorage.setItem('userEmail', userData.email);
                localStorage.setItem('userName', `${userData.firstName} ${userData.lastName}`);
                localStorage.setItem('userOrganization', userData.organization);
                localStorage.setItem('userSubscription', userData.subscription);
                
                // Show success message
                successMessage.textContent = 'Account created successfully! Redirecting to dashboard...';
                successMessage.style.display = 'block';
                
                // Redirect to dashboard after short delay
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);

            } catch (error) {
                // Show error message
                errorMessage.textContent = 'Failed to create account. Please try again.';
                errorMessage.style.display = 'block';
                
                // Reset button state
                signupBtn.disabled = false;
                signupText.style.display = 'inline';
                signupLoading.style.display = 'none';
            }
        });

        // Check if already authenticated
        if (localStorage.getItem('isAuthenticated') === 'true') {
            window.location.href = 'dashboard.html';
        }
    </script>
</body>
</html>
