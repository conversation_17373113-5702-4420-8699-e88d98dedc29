name: UI Contract Guard

on:
  pull_request:
    branches: [ "main" ]
  push:
    branches: [ "fix/**", "feature/**", "chore/**" ]

jobs:
  verify-ui-contract:
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4
      - name: Verify head/body contract
        run: |
          chmod +x scripts/verify_ui_contract.sh
          ./scripts/verify_ui_contract.sh | tee ui_contract_audit.csv
      - name: Upload audit CSV
        uses: actions/upload-artifact@v4
        with:
          name: ui-contract-audit
          path: ui_contract_audit.csv
      - name: Summarize
        run: |
          echo "## UI Contract Audit" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          sed -n '1,200p' ui_contract_audit.csv >> $GITHUB_STEP_SUMMARY

  http-smoke:
    runs-on: ubuntu-latest
    needs: verify-ui-contract
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@v4
      - name: Start tiny server
        run: |
          python -m http.server 5500 >/dev/null 2>&1 &
          echo $! > server.pid
          sleep 1
      - name: Derive pages and partials
        id: derive
        shell: bash
        run: |
          # Pages: all root-level HTML except index.html
          ls -1 *.html | grep -v '^index\.html$' | tr '\n' ' ' > pages.txt || true
          echo "PAGES=$(cat pages.txt)" >> $GITHUB_ENV

          # Extract fetch() targets from nav.js and footer.js, if any
          PARTIALS=""
          for f in nav.js footer.js; do
            if [ -f "$f" ]; then
              TARGETS=$(grep -Eo "fetch\(['\"][^'\"]+" "$f" | sed "s/fetch(['\"]//g" | tr '\n' ' ')
              PARTIALS="$PARTIALS $TARGETS"
            fi
          done
          echo "PARTIALS=$PARTIALS" >> $GITHUB_ENV

      - name: Curl pages
        run: |
          set -e
          for p in $PAGES; do
            echo "GET /$p"
            curl -sf "http://localhost:5500/$p" >/dev/null
          done

      - name: Curl partials (if any)
        run: |
          set -e
          for t in $PARTIALS; do
            # normalize leading slash
            path="$t"
            case "$path" in /*) : ;; *) path="/$path" ;; esac
            echo "GET $path"
            curl -sf "http://localhost:5500$path" >/dev/null
          done

      - name: Stop server
        if: always()
        run: |
          kill "$(cat server.pid)" 2>/dev/null || true
