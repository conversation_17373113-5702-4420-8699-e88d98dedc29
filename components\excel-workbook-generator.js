/**
 * Excel Workbook Generator
 * Converts HTML workbook data to real Excel files with preserved formulas
 * Integrates with existing FEMA workbook system
 */

class ExcelWorkbookGenerator {
    constructor() {
        this.isSheetJSLoaded = false;
        this.loadSheetJS();
    }

    /**
     * Load SheetJS library for Excel generation
     */
    async loadSheetJS() {
        if (window.XLSX) {
            this.isSheetJSLoaded = true;
            return;
        }

        try {
            // Load SheetJS from CDN
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = () => {
                this.isSheetJSLoaded = true;
                console.log('✅ SheetJS loaded successfully');
            };
            script.onerror = () => {
                console.error('❌ Failed to load SheetJS');
            };
            document.head.appendChild(script);
        } catch (error) {
            console.error('Error loading SheetJS:', error);
        }
    }

    /**
     * Generate real Excel workbook from HTML workbook data
     */
    async generateWorkbook(workbookData, filename = null) {
        if (!this.isSheetJSLoaded) {
            throw new Error('SheetJS not loaded. Please wait and try again.');
        }

        const wb = XLSX.utils.book_new();
        
        // Set workbook properties
        wb.Props = {
            Title: 'FEMA Project Workbook',
            Subject: 'FEMA Public Assistance Project Documentation',
            Author: 'ComplianceMax System',
            CreatedDate: new Date()
        };

        // Create all workbook sheets
        const sheets = await this.createAllSheets(workbookData);
        
        // Add sheets to workbook
        for (const [sheetName, sheetData] of Object.entries(sheets)) {
            XLSX.utils.book_append_sheet(wb, sheetData, sheetName);
        }

        // Generate filename if not provided
        if (!filename) {
            const timestamp = new Date().toISOString().split('T')[0];
            const projectId = workbookData.projectInfo?.id || 'project';
            filename = `FEMA_Workbook_${projectId}_${timestamp}.xlsx`;
        }

        // Download the Excel file
        XLSX.writeFile(wb, filename);
        
        return {
            success: true,
            filename: filename,
            sheets: Object.keys(sheets),
            message: 'Excel workbook generated successfully'
        };
    }

    /**
     * Create all sheets for the FEMA workbook
     */
    async createAllSheets(workbookData) {
        const sheets = {};

        // Sheet 1: Project Information
        sheets['Project Information'] = this.createProjectInfoSheet(workbookData.projectInfo || {});

        // Sheet 2: Labor Costs
        sheets['Labor Costs'] = this.createLaborCostsSheet(workbookData.laborCosts || []);

        // Sheet 3: Equipment Costs
        sheets['Equipment Costs'] = this.createEquipmentCostsSheet(workbookData.equipmentCosts || []);

        // Sheet 4: Materials Costs
        sheets['Materials Costs'] = this.createMaterialsCostsSheet(workbookData.materialsCosts || []);

        // Sheet 5: Contracts Other
        sheets['Contracts Other'] = this.createContractsCostsSheet(workbookData.contractsCosts || []);

        // Sheet 6: Insurance DOB
        sheets['Insurance DOB'] = this.createInsuranceDOBSheet(workbookData.insuranceDOB || {});

        // Sheet 7: Summary Totals
        sheets['Summary Totals'] = this.createSummarySheet(workbookData);

        // Sheet 8: BCA Analysis (if applicable)
        if (workbookData.bcaAnalysis) {
            sheets['BCA Analysis'] = this.createBCASheet(workbookData.bcaAnalysis);
        }

        return sheets;
    }

    /**
     * Create Project Information sheet (FEMA Form 90-91 compliant)
     */
    createProjectInfoSheet(projectInfo) {
        const data = [
            ['FEMA FORM 90-91 - PROJECT WORKSHEET', '', '', ''],
            ['PUBLIC ASSISTANCE PROGRAM', '', '', ''],
            ['', '', '', ''],
            ['SECTION A: APPLICANT INFORMATION', '', '', ''],
            ['Applicant Name', projectInfo.applicantName || '', '', ''],
            ['Applicant Type', projectInfo.applicantType || 'State/Local Government', '', ''],
            ['Contact Person', projectInfo.contactPerson || '', '', ''],
            ['Contact Phone', projectInfo.contactPhone || '', '', ''],
            ['Contact Email', projectInfo.contactEmail || '', '', ''],
            ['Mailing Address', projectInfo.mailingAddress || '', '', ''],
            ['', '', '', ''],
            ['SECTION B: PROJECT INFORMATION', '', '', ''],
            ['DR Number', projectInfo.drNumber || '', '', ''],
            ['Project Number', projectInfo.projectNumber || '', '', ''],
            ['Project Title', projectInfo.projectTitle || '', '', ''],
            ['Category of Work', projectInfo.category || '', '', ''],
            ['Work Type', projectInfo.workType || 'Permanent', '', ''],
            ['Incident Date', projectInfo.incidentDate || '', '', ''],
            ['Project Location', projectInfo.projectLocation || '', '', ''],
            ['Facility Name', projectInfo.facilityName || '', '', ''],
            ['', '', '', ''],
            ['SECTION C: DAMAGE DESCRIPTION', '', '', ''],
            ['Pre-Disaster Condition', projectInfo.preDisasterCondition || '', '', ''],
            ['Damage Description', projectInfo.damageDescription || '', '', ''],
            ['Scope of Work', projectInfo.scopeOfWork || '', '', ''],
            ['', '', '', ''],
            ['SECTION D: REGULATORY COMPLIANCE', '', '', ''],
            ['Environmental Review Required', projectInfo.environmentalReview || 'TBD', '', ''],
            ['Historic Preservation Review', projectInfo.historicPreservation || 'TBD', '', ''],
            ['Floodplain Management', projectInfo.floodplainManagement || 'N/A', '', ''],
            ['', '', '', ''],
            ['SECTION E: INSURANCE INFORMATION', '', '', ''],
            ['Insurance Coverage', projectInfo.insuranceCoverage || 'None', '', ''],
            ['Policy Number', projectInfo.policyNumber || '', '', ''],
            ['Settlement Amount', projectInfo.settlementAmount || 0, '', ''],
            ['Deductible Amount', projectInfo.deductible || 0, '', ''],
            ['', '', '', ''],
            ['Form Generated', new Date().toLocaleDateString(), '', ''],
            ['Generated By', 'ComplianceMax System', '', ''],
            ['PAPAG Version', projectInfo.papagVersion || 'v5.0', '', '']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        // Set column widths
        ws['!cols'] = [
            { wch: 30 }, // Field names
            { wch: 40 }, // Values
            { wch: 10 }, // Empty
            { wch: 10 }  // Empty
        ];

        // Style the headers
        if (ws['A1']) {
            ws['A1'].s = {
                font: { bold: true, sz: 14 },
                fill: { fgColor: { rgb: "4472C4" } },
                font: { color: { rgb: "FFFFFF" } }
            };
        }

        if (ws['A2']) {
            ws['A2'].s = {
                font: { bold: true, sz: 12 },
                fill: { fgColor: { rgb: "6C7B7F" } },
                font: { color: { rgb: "FFFFFF" } }
            };
        }

        // Style section headers
        const sectionRows = [4, 12, 22, 27, 31]; // Section header rows
        sectionRows.forEach(row => {
            const cellRef = 'A' + row;
            if (ws[cellRef]) {
                ws[cellRef].s = {
                    font: { bold: true },
                    fill: { fgColor: { rgb: "E7E6E6" } }
                };
            }
        });

        return ws;
    }

    /**
     * Create Labor Costs sheet with formulas
     */
    createLaborCostsSheet(laborCosts) {
        const headers = ['Description', 'Hours', 'Rate', 'Total', 'Notes'];
        const data = [headers];

        // Add labor cost items
        laborCosts.forEach((item, index) => {
            const rowNum = index + 2; // +2 because of header and 1-based indexing
            data.push([
                item.description || '',
                item.hours || 0,
                item.rate || 0,
                { f: `B${rowNum}*C${rowNum}` }, // Excel formula for total
                item.notes || ''
            ]);
        });

        // Add total row
        const totalRowNum = data.length + 1;
        data.push([
            'TOTAL LABOR COSTS',
            { f: `SUM(B2:B${totalRowNum - 1})` },
            '',
            { f: `SUM(D2:D${totalRowNum - 1})` },
            ''
        ]);

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        // Set column widths
        ws['!cols'] = [
            { wch: 25 }, // Description
            { wch: 10 }, // Hours
            { wch: 12 }, // Rate
            { wch: 15 }, // Total
            { wch: 20 }  // Notes
        ];

        // Format currency columns
        this.formatCurrencyColumn(ws, 'C', data.length); // Rate column
        this.formatCurrencyColumn(ws, 'D', data.length); // Total column

        return ws;
    }

    /**
     * Create Equipment Costs sheet with formulas
     */
    createEquipmentCostsSheet(equipmentCosts) {
        const headers = ['Equipment Description', 'Hours', 'Rate', 'Total', 'Notes'];
        const data = [headers];

        equipmentCosts.forEach((item, index) => {
            const rowNum = index + 2;
            data.push([
                item.description || '',
                item.hours || 0,
                item.rate || 0,
                { f: `B${rowNum}*C${rowNum}` },
                item.notes || ''
            ]);
        });

        const totalRowNum = data.length + 1;
        data.push([
            'TOTAL EQUIPMENT COSTS',
            { f: `SUM(B2:B${totalRowNum - 1})` },
            '',
            { f: `SUM(D2:D${totalRowNum - 1})` },
            ''
        ]);

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        ws['!cols'] = [
            { wch: 25 }, { wch: 10 }, { wch: 12 }, { wch: 15 }, { wch: 20 }
        ];

        this.formatCurrencyColumn(ws, 'C', data.length);
        this.formatCurrencyColumn(ws, 'D', data.length);

        return ws;
    }

    /**
     * Create Materials Costs sheet with formulas
     */
    createMaterialsCostsSheet(materialsCosts) {
        const headers = ['Material Description', 'Quantity', 'Unit Cost', 'Total', 'Unit', 'Notes'];
        const data = [headers];

        materialsCosts.forEach((item, index) => {
            const rowNum = index + 2;
            data.push([
                item.description || '',
                item.quantity || 0,
                item.unitCost || 0,
                { f: `B${rowNum}*C${rowNum}` },
                item.unit || '',
                item.notes || ''
            ]);
        });

        const totalRowNum = data.length + 1;
        data.push([
            'TOTAL MATERIALS COSTS',
            { f: `SUM(B2:B${totalRowNum - 1})` },
            '',
            { f: `SUM(D2:D${totalRowNum - 1})` },
            '',
            ''
        ]);

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        ws['!cols'] = [
            { wch: 25 }, { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 8 }, { wch: 20 }
        ];

        this.formatCurrencyColumn(ws, 'C', data.length);
        this.formatCurrencyColumn(ws, 'D', data.length);

        return ws;
    }

    /**
     * Create Contracts/Other Costs sheet
     */
    createContractsCostsSheet(contractsCosts) {
        const headers = ['Contract/Other Description', 'Amount', 'Notes'];
        const data = [headers];

        contractsCosts.forEach(item => {
            data.push([
                item.description || '',
                item.amount || 0,
                item.notes || ''
            ]);
        });

        const totalRowNum = data.length + 1;
        data.push([
            'TOTAL CONTRACTS/OTHER',
            { f: `SUM(B2:B${totalRowNum - 1})` },
            ''
        ]);

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        ws['!cols'] = [
            { wch: 30 }, { wch: 15 }, { wch: 25 }
        ];

        this.formatCurrencyColumn(ws, 'B', data.length);

        return ws;
    }

    /**
     * Create Insurance & DOB sheet
     */
    createInsuranceDOBSheet(insuranceData) {
        const data = [
            ['INSURANCE & DUPLICATION OF BENEFITS', '', ''],
            ['', '', ''],
            ['Insurance Information', '', ''],
            ['Insurance Coverage', insuranceData.coverage || '', ''],
            ['Policy Number', insuranceData.policyNumber || '', ''],
            ['Settlement Amount', insuranceData.settlementAmount || 0, ''],
            ['Deductible', insuranceData.deductible || 0, ''],
            ['', '', ''],
            ['Other Benefits', '', ''],
            ['SBA Loan Amount', insuranceData.sbaLoan || 0, ''],
            ['Other Federal Aid', insuranceData.otherFederalAid || 0, ''],
            ['State/Local Aid', insuranceData.stateLocalAid || 0, ''],
            ['', '', ''],
            ['Total DOB', { f: 'B6+B7+B10+B11+B12' }, ''],
            ['Net Eligible Amount', 'See Summary Sheet', '']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        ws['!cols'] = [
            { wch: 25 }, { wch: 20 }, { wch: 15 }
        ];

        this.formatCurrencyColumn(ws, 'B', data.length);

        return ws;
    }

    /**
     * Create Summary sheet with cross-sheet formulas
     */
    createSummarySheet(workbookData) {
        const data = [
            ['FEMA PROJECT COST SUMMARY', '', ''],
            ['', '', ''],
            ['Cost Category', 'Amount', 'Notes'],
            ['Labor Costs', { f: "'Labor Costs'!D" + (workbookData.laborCosts?.length + 2 || 2) }, ''],
            ['Equipment Costs', { f: "'Equipment Costs'!D" + (workbookData.equipmentCosts?.length + 2 || 2) }, ''],
            ['Materials Costs', { f: "'Materials Costs'!D" + (workbookData.materialsCosts?.length + 2 || 2) }, ''],
            ['Contracts/Other', { f: "'Contracts Other'!B" + (workbookData.contractsCosts?.length + 2 || 2) }, ''],
            ['', '', ''],
            ['SUBTOTAL', { f: 'SUM(B4:B7)' }, ''],
            ['', '', ''],
            ['Duplication of Benefits', { f: "'Insurance DOB'!B14" }, ''],
            ['', '', ''],
            ['NET FEMA ELIGIBLE AMOUNT', { f: 'B9-B11' }, ''],
            ['', '', ''],
            ['Federal Share (75%)', { f: 'B13*0.75' }, ''],
            ['Applicant Share (25%)', { f: 'B13*0.25' }, '']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        ws['!cols'] = [
            { wch: 25 }, { wch: 20 }, { wch: 15 }
        ];

        this.formatCurrencyColumn(ws, 'B', data.length);

        // Style important rows
        if (ws['B13']) { // Net FEMA Eligible
            ws['B13'].s = {
                font: { bold: true },
                fill: { fgColor: { rgb: "FFFF00" } }
            };
        }

        return ws;
    }

    /**
     * Create BCA Analysis sheet
     */
    createBCASheet(bcaData) {
        const data = [
            ['BENEFIT-COST ANALYSIS', '', ''],
            ['', '', ''],
            ['Project Cost', bcaData.projectCost || 0, ''],
            ['Annual Benefits', bcaData.annualBenefits || 0, ''],
            ['Project Life (years)', bcaData.projectLife || 50, ''],
            ['Discount Rate', bcaData.discountRate || 0.07, ''],
            ['', '', ''],
            ['Present Value of Benefits', { f: 'B4*((1-(1+B6)^(-B5))/B6)' }, ''],
            ['Benefit-Cost Ratio', { f: 'B8/B3' }, ''],
            ['', '', ''],
            ['BCA Result', { f: 'IF(B9>=1,"FAVORABLE","UNFAVORABLE")' }, '']
        ];

        const ws = XLSX.utils.aoa_to_sheet(data);
        
        ws['!cols'] = [
            { wch: 25 }, { wch: 20 }, { wch: 15 }
        ];

        this.formatCurrencyColumn(ws, 'B', data.length);

        return ws;
    }

    /**
     * Format currency columns
     */
    formatCurrencyColumn(ws, column, rowCount) {
        for (let i = 2; i <= rowCount; i++) {
            const cellRef = column + i;
            if (ws[cellRef] && typeof ws[cellRef].v === 'number') {
                ws[cellRef].z = '"$"#,##0.00';
            }
        }
    }

    /**
     * Collect workbook data from HTML form
     */
    collectWorkbookDataFromHTML() {
        const workbookData = {
            projectInfo: this.collectProjectInfo(),
            laborCosts: this.collectCostItems('labor-items-container'),
            equipmentCosts: this.collectCostItems('equipment-items-container'),
            materialsCosts: this.collectCostItems('materials-items-container'),
            contractsCosts: this.collectCostItems('contracts-items-container'),
            insuranceDOB: this.collectInsuranceData()
        };

        return workbookData;
    }

    /**
     * Collect project information from HTML form
     */
    collectProjectInfo() {
        return {
            applicantName: this.getElementValue('wb-applicant-name'),
            projectTitle: this.getElementValue('wb-project-title'),
            category: this.getElementValue('wb-category'),
            papagVersion: this.getElementValue('wb-papag-version'),
            drNumber: this.getElementValue('wb-dr-number'),
            incidentDate: this.getElementValue('wb-incident-date'),
            projectLocation: this.getElementValue('wb-project-location'),
            damageDescription: this.getElementValue('wb-damage-description'),
            insuranceCoverage: this.getElementValue('wb-insurance-coverage'),
            settlementAmount: this.getElementValue('wb-settlement-amount')
        };
    }

    /**
     * Collect cost items from HTML containers
     */
    collectCostItems(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return [];

        const items = [];
        const costItems = container.querySelectorAll('.cost-item');

        costItems.forEach(item => {
            const inputs = item.querySelectorAll('input');
            if (inputs.length >= 3) {
                items.push({
                    description: inputs[0].value || '',
                    hours: parseFloat(inputs[1].value) || 0,
                    rate: parseFloat(inputs[2].value) || 0,
                    notes: inputs[4]?.value || ''
                });
            }
        });

        return items;
    }

    /**
     * Collect insurance data from HTML form
     */
    collectInsuranceData() {
        return {
            coverage: this.getElementValue('wb-insurance-coverage'),
            settlementAmount: parseFloat(this.getElementValue('wb-settlement-amount')?.replace(/[$,]/g, '')) || 0,
            deductible: parseFloat(this.getElementValue('wb-deductible')?.replace(/[$,]/g, '')) || 0,
            sbaLoan: parseFloat(this.getElementValue('wb-sba-loan')?.replace(/[$,]/g, '')) || 0,
            otherFederalAid: parseFloat(this.getElementValue('wb-other-federal')?.replace(/[$,]/g, '')) || 0,
            stateLocalAid: parseFloat(this.getElementValue('wb-state-local')?.replace(/[$,]/g, '')) || 0
        };
    }

    /**
     * Get element value safely
     */
    getElementValue(elementId) {
        const element = document.getElementById(elementId);
        return element ? element.value : '';
    }

    /**
     * Generate Excel workbook from current HTML workbook
     */
    async exportCurrentWorkbook(filename = null) {
        try {
            const workbookData = this.collectWorkbookDataFromHTML();
            const result = await this.generateWorkbook(workbookData, filename);
            
            // Show success message
            this.showExportSuccess(result);
            
            return result;
        } catch (error) {
            console.error('Error exporting workbook:', error);
            this.showExportError(error);
            throw error;
        }
    }

    /**
     * Show export success message
     */
    showExportSuccess(result) {
        const message = `✅ Excel Workbook Generated Successfully!\n\nFile: ${result.filename}\nSheets: ${result.sheets.join(', ')}\n\nThe workbook includes:\n• All cost categories with formulas\n• Cross-sheet calculations\n• FEMA-compliant structure\n• Ready for submission`;
        
        if (window.showNotification) {
            window.showNotification(message, 'success');
        } else {
            alert(message);
        }
    }

    /**
     * Show export error message
     */
    showExportError(error) {
        const message = `❌ Error Generating Excel Workbook\n\nError: ${error.message}\n\nPlease try again or contact support if the issue persists.`;
        
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

// Create global instance
window.excelGenerator = new ExcelWorkbookGenerator();

// Export for module use
export { ExcelWorkbookGenerator };
