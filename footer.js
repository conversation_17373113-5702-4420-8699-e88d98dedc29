(async function mountFooter(){
  const holder = document.getElementById('cmx-footer');
  if (!holder) return;
  
  try{
    const res = await fetch('partials/footer.html', {cache:'no-store'});
    holder.innerHTML = await res.text();
  }catch(e){ 
    console.warn('footer load failed', e); 
    // Fallback footer if fetch fails
    holder.innerHTML = `
      <footer id="legal-footer" style="font:12px/1.4 system-ui;color:#475569;padding:10px 16px;border-top:1px solid #e2e8f0">
        <div>
          © 2025 Max <PERSON>, <PERSON> & <PERSON>l. All rights reserved.
          <span> Not an official FEMA system or submission portal; no affiliation, endorsement, or sponsorship by FEMA.</span>
          <span> For informational purposes only; not legal, engineering, or accounting advice. Verify against current FEMA policy.</span>
          <span> All trademarks belong to their respective owners.</span>
          <a href="legal.html" style="margin-left:8px;text-decoration:underline;">Legal</a>
        </div>
      </footer>
    `;
  }
})();
