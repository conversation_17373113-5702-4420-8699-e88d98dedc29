// Max Hints - Sidewalk tooltip system with step-aware advice
(function() {
    'use strict';
    
    let hintsData = {};
    let currentHint = null;
    let isLoading = false;
    
    // Initialize hints system
    async function initHints() {
        try {
            // Load hints data
            const response = await fetch('ui/sidewalk_hints.json');
            if (response.ok) {
                hintsData = await response.json();
                console.log('✅ Max Hints loaded:', Object.keys(hintsData).length, 'hints');
            } else {
                console.warn('⚠️ Could not load hints data');
                return;
            }
            
            // Bind to step elements
            bindHints();
            
        } catch (error) {
            console.warn('⚠️ Hints initialization failed:', error);
        }
    }
    
    // Bind hint triggers to step elements
    function bindHints() {
        const stepElements = document.querySelectorAll('[data-flow][data-step], .form-step[data-step], .step[data-step]');
        
        stepElements.forEach(element => {
            const hintKey = getHintKey(element);
            if (hintKey && hintsData[hintKey]) {
                setupHintTrigger(element, hintKey);
            }
        });
        
        console.log('✅ Bound hints to', stepElements.length, 'step elements');
    }
    
    // Get hint key from element
    function getHintKey(element) {
        // Check for explicit hint key
        const explicitKey = element.getAttribute('data-hint-key');
        if (explicitKey) return explicitKey;
        
        // Compose from flow and step
        const flow = element.getAttribute('data-flow') || 
                    element.closest('[data-flow]')?.getAttribute('data-flow');
        const step = element.getAttribute('data-step');
        
        if (flow && step) {
            return `${flow}:${step}`;
        }
        
        return null;
    }
    
    // Setup hint trigger for element
    function setupHintTrigger(element, hintKey) {
        let hoverTimeout;
        
        // Mouse events
        element.addEventListener('mouseenter', () => {
            hoverTimeout = setTimeout(() => showHint(element, hintKey), 300);
        });
        
        element.addEventListener('mouseleave', () => {
            clearTimeout(hoverTimeout);
            if (currentHint && !currentHint.matches(':hover')) {
                hideHint();
            }
        });
        
        // Touch events
        element.addEventListener('touchstart', (e) => {
            e.preventDefault();
            showHint(element, hintKey);
        });
        
        // Keyboard events
        element.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                showHint(element, hintKey);
            }
        });
        
        // Make focusable
        if (!element.hasAttribute('tabindex')) {
            element.setAttribute('tabindex', '0');
        }
    }
    
    // Show hint cloud
    function showHint(element, hintKey) {
        hideHint(); // Hide any existing hint
        
        const hintData = hintsData[hintKey];
        if (!hintData) return;
        
        // Create hint cloud
        const cloud = createHintCloud(hintData, hintKey);
        
        // Position cloud
        positionHint(cloud, element);
        
        // Add to DOM
        document.body.appendChild(cloud);
        currentHint = cloud;
        
        // Show with animation
        requestAnimationFrame(() => {
            cloud.classList.add('visible');
        });
        
        // Setup close handlers
        setupCloseHandlers(cloud);
    }
    
    // Create hint cloud element
    function createHintCloud(hintData, hintKey) {
        const cloud = document.createElement('div');
        cloud.className = 'hint-cloud';
        cloud.setAttribute('role', 'tooltip');
        cloud.setAttribute('aria-live', 'polite');
        
        cloud.innerHTML = `
            <div class="hint-title">${escapeHtml(hintData.title)}</div>
            
            <div class="hint-section">
                <div class="hint-label">Why</div>
                <div class="hint-text">${escapeHtml(hintData.why)}</div>
            </div>
            
            <div class="hint-section">
                <div class="hint-label">What</div>
                <div class="hint-text">${escapeHtml(hintData.what)}</div>
            </div>
            
            <div class="hint-actions">
                ${hintData.more_url ? `<a href="${escapeHtml(hintData.more_url)}" class="hint-link" target="_blank">Learn more</a>` : ''}
                ${hintData.api_key ? `<button class="hint-cta" data-api-key="${escapeHtml(hintData.api_key)}">Get step recommendations</button>` : ''}
            </div>
            
            <div class="recommendations-container" style="display: none;"></div>
        `;
        
        // Bind CTA button
        const ctaButton = cloud.querySelector('.hint-cta');
        if (ctaButton) {
            ctaButton.addEventListener('click', () => {
                loadRecommendations(cloud, hintData, hintKey);
            });
        }
        
        return cloud;
    }
    
    // Position hint cloud relative to element
    function positionHint(cloud, element) {
        const rect = element.getBoundingClientRect();
        const cloudRect = { width: 320, height: 200 }; // Estimated
        
        // Determine vertical position
        const spaceAbove = rect.top;
        const spaceBelow = window.innerHeight - rect.bottom;
        const preferAbove = spaceAbove > cloudRect.height + 20;
        
        if (preferAbove) {
            cloud.classList.add('above');
            cloud.style.top = `${rect.top + window.scrollY - cloudRect.height - 8}px`;
        } else {
            cloud.classList.add('below');
            cloud.style.top = `${rect.bottom + window.scrollY + 8}px`;
        }
        
        // Determine horizontal position
        const spaceLeft = rect.left;
        const spaceRight = window.innerWidth - rect.right;
        
        if (spaceLeft > cloudRect.width / 2 && spaceRight > cloudRect.width / 2) {
            // Center if enough space
            cloud.classList.add('center');
            cloud.style.left = `${rect.left + rect.width / 2}px`;
        } else if (spaceRight > cloudRect.width) {
            // Align left
            cloud.classList.add('right');
            cloud.style.left = `${rect.left}px`;
        } else {
            // Align right
            cloud.classList.add('left');
            cloud.style.left = `${rect.right - cloudRect.width}px`;
        }
    }
    
    // Setup close handlers
    function setupCloseHandlers(cloud) {
        // Close on escape
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                hideHint();
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);
        
        // Close on outside click
        const clickHandler = (e) => {
            if (!cloud.contains(e.target)) {
                hideHint();
                document.removeEventListener('click', clickHandler);
            }
        };
        setTimeout(() => {
            document.addEventListener('click', clickHandler);
        }, 100);
        
        // Close on scroll
        const scrollHandler = () => {
            hideHint();
            window.removeEventListener('scroll', scrollHandler);
        };
        window.addEventListener('scroll', scrollHandler);
    }
    
    // Hide current hint
    function hideHint() {
        if (currentHint) {
            currentHint.classList.remove('visible');
            setTimeout(() => {
                if (currentHint && currentHint.parentNode) {
                    currentHint.parentNode.removeChild(currentHint);
                }
                currentHint = null;
            }, 200);
        }
    }
    
    // Load recommendations from backend
    async function loadRecommendations(cloud, hintData, hintKey) {
        if (isLoading) return;
        
        const container = cloud.querySelector('.recommendations-container');
        const ctaButton = cloud.querySelector('.hint-cta');
        
        isLoading = true;
        ctaButton.disabled = true;
        ctaButton.innerHTML = '<span class="loading"></span> Loading...';
        
        try {
            // Extract context from current page
            const context = getPageContext(hintKey);
            
            // Call advice API
            const response = await fetch('/advice/step', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    flow: hintKey.split(':')[0],
                    step: hintKey.split(':')[1],
                    api_key: hintData.api_key,
                    context: context
                })
            });
            
            if (response.ok) {
                const advice = await response.json();
                displayRecommendations(container, advice);
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
            
        } catch (error) {
            console.warn('⚠️ Failed to load recommendations:', error);
            container.innerHTML = '<div class="recommendations-error">No recommendations available yet</div>';
        } finally {
            isLoading = false;
            ctaButton.disabled = false;
            ctaButton.textContent = 'Get step recommendations';
            container.style.display = 'block';
        }
    }
    
    // Get page context for recommendations
    function getPageContext(hintKey) {
        return {
            event_year: 2024,
            disaster_code: 'DR-4781',
            applicant_type: 'local_gov',
            inputs: {}
        };
    }
    
    // Display recommendations in container
    function displayRecommendations(container, advice) {
        let html = '';
        
        if (advice.summary) {
            html += `<div class="recommendations-summary">${escapeHtml(advice.summary)}</div>`;
        }
        
        if (advice.recommendations && advice.recommendations.length > 0) {
            advice.recommendations.forEach(rec => {
                html += `
                    <div class="recommendation-item ${rec.severity || 'medium'}">
                        <div class="recommendation-title">${escapeHtml(rec.title)}</div>
                        <div class="recommendation-why">${escapeHtml(rec.why)}</div>
                        <div class="recommendation-actions">
                            <ul>
                                ${rec.actions.map(action => `<li>${escapeHtml(action)}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `;
            });
        }
        
        if (advice.policy_refs && advice.policy_refs.length > 0) {
            html += `
                <div class="policy-refs">
                    <div class="policy-refs-title">Policy References:</div>
                    ${advice.policy_refs.map(ref => 
                        `<a href="${escapeHtml(ref.url)}" class="policy-ref" target="_blank">${escapeHtml(ref.doc_id)} §${escapeHtml(ref.section)}</a>`
                    ).join('')}
                </div>
            `;
        }
        
        container.innerHTML = html;
    }
    
    // Utility: Escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initHints);
    } else {
        initHints();
    }
    
})();
