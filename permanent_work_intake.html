<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permanent Work Intake - ComplianceMax</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <link rel="stylesheet" href="ui/sidewalk-hints.css">
    <link rel="stylesheet" href="print.css" media="print">
    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <script defer src="fema_workbook_integration.js"></script>
    <script defer src="js/sidewalk-hints.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            min-height: 100vh;
            padding: 0;
        }

        .top-nav {
            background: linear-gradient(135deg, #253464 0%, #1e2a5a 100%);
            color: white;
            padding: 15px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 40px;
        }

        .nav-logo {
            font-size: 1.8em;
            font-weight: bold;
            color: #ffc107;
        }

        .main-nav {
            display: flex;
            align-items: center;
            gap: 24px;
            flex: 1;
            justify-content: center;
        }

        .nav-item {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            white-space: nowrap;
        }

        .nav-item:hover {
            color: white;
            background: rgba(255,255,255,0.1);
        }

        .nav-item.active {
            color: #ffc107;
            background: rgba(255,193,7,0.1);
        }

        .page-container {
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 60px 30px 20px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '🏗️';
            position: absolute;
            top: 15px;
            left: 30px;
            font-size: 2em;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9em;
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .form-container {
            padding: 40px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
            position: relative;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 20px;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: #059669;
            color: white;
        }

        .step.completed .step-number {
            background: #10b981;
            color: white;
        }

        .step-label {
            font-weight: 500;
            color: #6b7280;
        }

        .step.active .step-label {
            color: #059669;
        }

        .step.completed .step-label {
            color: #10b981;
        }

        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .required {
            color: #dc2626;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .papag-info {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .papag-info.show {
            display: block;
        }

        .papag-info h4 {
            color: #0369a1;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .papag-info p {
            color: #0c4a6e;
            margin-bottom: 8px;
        }

        .category-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .category-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-card:hover {
            border-color: #059669;
            background: #f0fdf4;
            transform: translateY(-2px);
        }

        .category-card.selected {
            border-color: #059669;
            background: #f0fdf4;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
        }

        .category-card .category-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .category-card .category-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .category-card .category-desc {
            font-size: 0.9em;
            color: #6b7280;
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #059669;
            color: white;
        }

        .btn-primary:hover {
            background: #047857;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn:disabled {
            background: #d1d5db;
            color: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            color: #dc2626;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .success-message {
            background: #f0fdf4;
            border: 2px solid #22c55e;
            color: #15803d;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .compliance-pods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .compliance-pod {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
        }

        .pod-title {
            background: #059669;
            color: white;
            padding: 15px;
            font-weight: bold;
            text-align: center;
        }

        .pod-content {
            padding: 20px;
        }

        .pod-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .pod-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.95em;
        }

        .pod-list li:last-child {
            border-bottom: none;
        }

        .review-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .review-section h4 {
            color: #059669;
            margin: 20px 0 10px 0;
            font-size: 1.1em;
        }

        .review-section h4:first-child {
            margin-top: 0;
        }

        .review-item {
            margin: 8px 0;
            padding: 5px 0;
        }

        .review-label {
            font-weight: 600;
            color: #374151;
            display: inline-block;
            width: 150px;
        }

        .review-value {
            color: #6b7280;
        }

        .upload-zone {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f9fafb;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: #059669;
            background: #f0fdf4;
        }

        .upload-zone.dragover {
            border-color: #059669;
            background: #f0fdf4;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1em;
            color: #374151;
            margin-bottom: 10px;
        }

        .uploaded-file {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-info {
            display: flex;
            align-items: center;
        }

        .file-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 600;
            color: #374151;
        }

        .file-size {
            font-size: 0.9em;
            color: #6b7280;
        }

        .file-actions {
            display: flex;
            gap: 10px;
        }

        .file-action {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .file-action:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .cbcs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .cbcs-code-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cbcs-code-card:hover {
            border-color: #059669;
            background: #f0fdf4;
        }

        .cbcs-code-card.selected {
            border-color: #059669;
            background: #f0fdf4;
            box-shadow: 0 2px 8px rgba(5, 150, 105, 0.2);
        }

        .cbcs-code {
            font-weight: bold;
            color: #059669;
            margin-bottom: 5px;
        }

        .cbcs-description {
            font-size: 0.9em;
            color: #6b7280;
        }

        @media (max-width: 768px) {
            .compliance-pods {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .category-selection {
                grid-template-columns: repeat(2, 1fr);
            }

            .cbcs-grid {
                grid-template-columns: 1fr;
            }

            .upload-zone {
                padding: 20px;
            }
        }
    </style>
</head>
<body data-page="permanent-intake" data-flow="permanent" data-step="1">
    <div id="universal-nav" class="site-nav"></div>

<main id="main" class="page-container">
    <div class="container">
        <div class="header">
            <a href="landing_page.html" class="back-btn">← Back</a>
            <div class="title">Permanent Work Intake</div>
            <div class="subtitle">Categories C, D, E, F & G - Comprehensive PA Request</div>
        </div>

        <div class="form-container">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">Basic Information</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">Project Details</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">Compliance Pods</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-label">Insurance & DOB</div>
                </div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-label">Document Upload</div>
                </div>
                <div class="step" data-step="6">
                    <div class="step-number">6</div>
                    <div class="step-label">CBCS Codes</div>
                </div>
                <div class="step" data-step="7">
                    <div class="step-number">7</div>
                    <div class="step-label">Mitigation & EHP</div>
                </div>
                <div class="step" data-step="8">
                    <div class="step-number">8</div>
                    <div class="step-label">Procurement</div>
                </div>
                <div class="step" data-step="9">
                    <div class="step-number">9</div>
                    <div class="step-label">FEMA Workbook</div>
                </div>
                <div class="step" data-step="10">
                    <div class="step-number">10</div>
                    <div class="step-label">Review & Submit</div>
                </div>
            </div>

            <form id="permanentIntakeForm">
                <!-- STEP 1: Basic Information -->
                <div class="form-step active" data-step="1">
                    <h3 style="color: #059669; margin-bottom: 20px;">📋 Applicant Information</h3>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="applicantName">Applicant Name <span class="required">*</span></label>
                            <input type="text" id="applicantName" name="applicantName" required 
                                   placeholder="Enter organization/entity name">
                        </div>

                        <div class="form-group">
                            <label for="applicantType">Applicant Type <span class="required">*</span></label>
                            <select id="applicantType" name="applicantType" required>
                                <option value="">Select applicant type</option>
                                <option value="State Government">State Government</option>
                                <option value="Local Government">Local Government</option>
                                <option value="Tribal Government">Tribal Government</option>
                                <option value="Territory/Commonwealth">Territory/Commonwealth</option>
                                <option value="Private Non-Profit Organization">Private Non-Profit Organization</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="contactPerson">Primary Contact <span class="required">*</span></label>
                            <input type="text" id="contactPerson" name="contactPerson" required 
                                   placeholder="Contact person name">
                        </div>

                        <div class="form-group">
                            <label for="contactEmail">Contact Email <span class="required">*</span></label>
                            <input type="email" id="contactEmail" name="contactEmail" required 
                                   placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="contactPhone">Contact Phone <span class="required">*</span></label>
                            <input type="tel" id="contactPhone" name="contactPhone" required 
                                   placeholder="(*************">
                        </div>

                        <div class="form-group">
                            <label for="disasterNumber">Disaster Number (DR#)</label>
                            <input type="text" id="disasterNumber" name="disasterNumber" 
                                   placeholder="DR-XXXX-XX" pattern="DR-\d{4}-\w{2}">
                            <div class="error-message" id="drError" style="display: none;"></div>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="applicantAddress">Applicant Address <span class="required">*</span></label>
                            <textarea id="applicantAddress" name="applicantAddress" required 
                                      placeholder="Street address, city, state, ZIP code"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="incidentDate">Incident Date <span class="required">*</span></label>
                            <input type="date" id="incidentDate" name="incidentDate" required>
                            <div class="error-message" id="dateError" style="display: none;"></div>
                        </div>
                    </div>

                    <!-- PAPAG Version Information -->
                    <div class="papag-info" id="papagInfo">
                        <h4>📚 Applicable PAPAG Version</h4>
                        <p id="papagVersion"></p>
                        <p id="papagDetails"></p>
                    </div>
                </div>

                <!-- STEP 2: Project Details -->
                <div class="form-step" data-step="2">
                    <h3 style="color: #059669; margin-bottom: 20px;">🏗️ Project Information</h3>

                    <div class="form-group">
                        <label for="projectTitle">Project Title <span class="required">*</span></label>
                        <input type="text" id="projectTitle" name="projectTitle" required
                               placeholder="Brief descriptive title for this project">
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="projectLocation">Project Location <span class="required">*</span></label>
                            <textarea id="projectLocation" name="projectLocation" required
                                      placeholder="Specific address, GPS coordinates, or detailed location description"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="estimatedCost">Estimated Project Cost</label>
                            <input type="number" id="estimatedCost" name="estimatedCost"
                                   placeholder="0.00" step="0.01" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="damageDescription">Damage Description <span class="required">*</span></label>
                        <textarea id="damageDescription" name="damageDescription" required
                                  placeholder="Detailed description of damage caused by the disaster"
                                  style="min-height: 120px;"></textarea>
                    </div>

                    <h4 style="color: #059669; margin: 30px 0 20px 0;">📂 Select Work Category</h4>
                    <div class="category-selection">
                        <div class="category-card" data-category="C">
                            <div class="category-icon">🛣️</div>
                            <div class="category-title">Category C</div>
                            <div class="category-desc">Roads & Bridges</div>
                        </div>
                        <div class="category-card" data-category="D">
                            <div class="category-icon">🌊</div>
                            <div class="category-title">Category D</div>
                            <div class="category-desc">Water Control Facilities</div>
                        </div>
                        <div class="category-card" data-category="E">
                            <div class="category-icon">🏢</div>
                            <div class="category-title">Category E</div>
                            <div class="category-desc">Buildings & Equipment</div>
                        </div>
                        <div class="category-card" data-category="F">
                            <div class="category-icon">⚡</div>
                            <div class="category-title">Category F</div>
                            <div class="category-desc">Utilities</div>
                        </div>
                        <div class="category-card" data-category="G">
                            <div class="category-icon">🏞️</div>
                            <div class="category-title">Category G</div>
                            <div class="category-desc">Parks & Recreation</div>
                        </div>
                    </div>
                    <input type="hidden" id="workCategory" name="workCategory" required>
                    <div class="error-message" id="categoryError" style="display: none;">Please select a work category</div>
                </div>

                <!-- STEP 3: Compliance Pods -->
                <div class="form-step" data-step="3">
                    <h3 style="color: #059669; margin-bottom: 20px;">📋 Documentation Requirements</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">Based on your selected category, here are the required documents and system analysis resources:</p>

                    <div class="compliance-pods">
                        <div class="compliance-pod">
                            <div class="pod-title">
                                📄 Required from Applicant
                            </div>
                            <div class="pod-content">
                                <ul class="pod-list" id="applicantDocs">
                                    <!-- Populated based on category selection -->
                                </ul>
                            </div>
                        </div>
                        <div class="compliance-pod">
                            <div class="pod-title">
                                📚 System Analysis Resources
                            </div>
                            <div class="pod-content">
                                <ul class="pod-list" id="systemDocs">
                                    <!-- Populated based on category selection -->
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" style="margin-top: 30px;">
                        <label for="additionalNotes">Additional Notes or Special Circumstances</label>
                        <textarea id="additionalNotes" name="additionalNotes"
                                  placeholder="Any additional information, special circumstances, or notes about this project"
                                  style="min-height: 100px;"></textarea>
                    </div>
                </div>

                <!-- STEP 4: Insurance & Duplication of Benefits -->
                <div class="form-step" data-step="4">
                    <h3 style="color: #059669; margin-bottom: 20px;">💰 Insurance & Duplication of Benefits</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">FEMA cannot duplicate benefits from other sources. All insurance and other assistance must be documented per PAPPG Section VIII.A.</p>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="hasInsurance">Do you have insurance coverage for this facility? <span class="required">*</span></label>
                            <select id="hasInsurance" name="hasInsurance" required onchange="toggleInsuranceDetails()">
                                <option value="">Select insurance status</option>
                                <option value="yes">Yes - Full Coverage</option>
                                <option value="partial">Yes - Partial Coverage</option>
                                <option value="no">No Insurance Coverage</option>
                                <option value="unknown">Unknown/Under Review</option>
                            </select>
                        </div>

                        <div class="form-group" id="insuranceTypeGroup" style="display: none;">
                            <label for="insuranceTypes">Types of Insurance Coverage</label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-top: 8px;">
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="insuranceTypes" value="property" style="margin-right: 8px;">
                                    General Property Insurance
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="insuranceTypes" value="flood" style="margin-right: 8px;">
                                    Flood Insurance (NFIP)
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="insuranceTypes" value="business" style="margin-right: 8px;">
                                    Business Interruption
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="insuranceTypes" value="special" style="margin-right: 8px;">
                                    Special Hazard Insurance
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-grid" id="insuranceDetailsGroup" style="display: none;">
                        <div class="form-group">
                            <label for="insuranceCarrier">Insurance Carrier</label>
                            <input type="text" id="insuranceCarrier" name="insuranceCarrier"
                                   placeholder="Name of insurance company">
                        </div>

                        <div class="form-group">
                            <label for="policyNumber">Policy Number</label>
                            <input type="text" id="policyNumber" name="policyNumber"
                                   placeholder="Insurance policy number">
                        </div>

                        <div class="form-group">
                            <label for="coverageAmount">Coverage Amount</label>
                            <input type="number" id="coverageAmount" name="coverageAmount"
                                   placeholder="0.00" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="deductibleAmount">Deductible Amount</label>
                            <input type="number" id="deductibleAmount" name="deductibleAmount"
                                   placeholder="0.00" step="0.01" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="insuranceStatus">Insurance Claim Status <span class="required">*</span></label>
                        <select id="insuranceStatus" name="insuranceStatus" required onchange="toggleSettlementDetails()">
                            <option value="">Select claim status</option>
                            <option value="no-claim">No Claim Filed</option>
                            <option value="pending">Claim Filed - Pending</option>
                            <option value="partial">Partial Settlement Received</option>
                            <option value="full">Full Settlement Received</option>
                            <option value="denied">Claim Denied</option>
                        </select>
                    </div>

                    <div class="form-grid" id="settlementDetailsGroup" style="display: none;">
                        <div class="form-group">
                            <label for="settlementAmount">Settlement Amount Received</label>
                            <input type="number" id="settlementAmount" name="settlementAmount"
                                   placeholder="0.00" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="settlementDate">Settlement Date</label>
                            <input type="date" id="settlementDate" name="settlementDate">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="otherAssistance">Other Federal, State, or Local Assistance</label>
                        <textarea id="otherAssistance" name="otherAssistance"
                                  placeholder="List any other assistance received or applied for (SBA loans, state grants, local assistance, etc.)"
                                  style="min-height: 80px;"></textarea>
                    </div>

                    <div style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 20px 0;">
                        <h4 style="color: #92400e; margin-bottom: 10px;">⚠️ Duplication of Benefits Notice</h4>
                        <p style="color: #92400e; font-size: 0.9em;">
                            FEMA assistance cannot duplicate benefits from insurance or other sources. Any insurance proceeds or other assistance received must be deducted from FEMA assistance per the Stafford Act Section 312.
                        </p>
                    </div>
                </div>

                <!-- STEP 5: Document Upload -->
                <div class="form-step" data-step="5">
                    <h3 style="color: #059669; margin-bottom: 20px;">📤 Document Upload</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">Upload required documents based on your selected category. PDFs will be automatically processed to suggest CBCS codes.</p>

                    <!-- Upload Zone -->
                    <div class="upload-zone" id="documentUploadZone">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            Drag & drop files here or click to browse
                        </div>
                        <div style="font-size: 0.9em; color: #6b7280; margin-bottom: 15px;">
                            Supports: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (Max 50MB each)
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="document.getElementById('documentFileInput').click()">
                            📁 Choose Files
                        </button>
                        <input type="file" id="documentFileInput" multiple
                               accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" style="display: none;">
                    </div>

                    <!-- Uploaded Files List -->
                    <div id="uploadedFilesList" style="margin-top: 30px;"></div>

                    <!-- Processing Status -->
                    <div id="processingStatus" style="display: none; margin-top: 20px;">
                        <div style="background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 8px; padding: 15px;">
                            <h4 style="color: #0369a1; margin-bottom: 10px;">🔄 Processing Documents...</h4>
                            <div class="progress-bar" style="background: #e5e7eb; border-radius: 4px; height: 8px; overflow: hidden;">
                                <div id="processingProgress" style="background: #0ea5e9; height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                            <p id="processingText" style="color: #0c4a6e; margin-top: 8px; font-size: 0.9em;">Analyzing documents...</p>
                        </div>
                    </div>
                </div>

                <!-- STEP 6: CBCS Codes Selection -->
                <div class="form-step" data-step="6">
                    <h3 style="color: #059669; margin-bottom: 20px;">🔍 CBCS Codes & Standards</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">Select applicable CBCS codes based on your project requirements and uploaded documents.</p>

                    <!-- Auto-suggested codes from document analysis -->
                    <div id="suggestedCodes" style="display: none; margin-bottom: 30px;">
                        <div style="background: #f0fdf4; border: 2px solid #22c55e; border-radius: 8px; padding: 15px;">
                            <h4 style="color: #15803d; margin-bottom: 10px;">🤖 AI-Suggested Codes</h4>
                            <p style="color: #166534; font-size: 0.9em; margin-bottom: 15px;">Based on your uploaded documents, we suggest these CBCS codes:</p>
                            <div id="suggestedCodesList"></div>
                        </div>
                    </div>

                    <!-- CBCS Codes Grid -->
                    <div class="cbcs-codes-section">
                        <h4 style="margin-bottom: 15px;">Available CBCS Codes for <span id="categoryDisplay"></span></h4>
                        <div id="cbcsCodesGrid" class="cbcs-grid">
                            <!-- Populated based on selected category -->
                        </div>
                    </div>

                    <!-- Technical Justification -->
                    <div style="margin-top: 30px;">
                        <label for="technicalJustification">Technical Justification <span class="required">*</span></label>
                        <textarea id="technicalJustification" name="technicalJustification" required
                                  placeholder="Provide technical justification for the selected CBCS codes and project approach"
                                  style="min-height: 120px;"></textarea>
                        <button type="button" class="btn btn-secondary" onclick="generateJustification()"
                                style="margin-top: 10px;">
                            🤖 Auto-Generate Justification
                        </button>
                    </div>
                </div>

                <!-- STEP 7: Mitigation & EHP Compliance -->
                <div class="form-step" data-step="7">
                    <h3 style="color: #059669; margin-bottom: 20px;">🏗️ Section 406 Mitigation & EHP Compliance</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">All permanent work requires mitigation analysis per 44 CFR 206.226 and EHP review per 44 CFR Part 10.</p>

                    <!-- Mitigation Analysis Section -->
                    <div style="background: #fef3c7; border: 2px solid #f59e0b; border-radius: 12px; padding: 20px; margin-bottom: 30px;">
                        <h4 style="color: #92400e; margin-bottom: 15px;">🏗️ Section 406 Mitigation Analysis</h4>

                        <div class="form-group">
                            <label for="mitigationRequired">Is this project over $1 million? <span class="required">*</span></label>
                            <select id="mitigationRequired" name="mitigationRequired" required onchange="toggleMitigationDetails()">
                                <option value="">Select project size</option>
                                <option value="under1m">Under $1 Million</option>
                                <option value="over1m">Over $1 Million (Enhanced Analysis Required)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="hazardTypes">Primary Hazards to Mitigate <span class="required">*</span></label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-top: 8px;">
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="hazardTypes" value="flood" style="margin-right: 8px;">
                                    Flood
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="hazardTypes" value="wind" style="margin-right: 8px;">
                                    Wind/Hurricane
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="hazardTypes" value="seismic" style="margin-right: 8px;">
                                    Seismic/Earthquake
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="hazardTypes" value="wildfire" style="margin-right: 8px;">
                                    Wildfire
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="hazardTypes" value="tornado" style="margin-right: 8px;">
                                    Tornado
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="hazardTypes" value="other" style="margin-right: 8px;">
                                    Other
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="mitigationMeasures">Proposed Mitigation Measures</label>
                            <textarea id="mitigationMeasures" name="mitigationMeasures"
                                      placeholder="Describe specific mitigation measures being considered (elevation, strengthening, relocation, etc.)"
                                      style="min-height: 100px;"></textarea>
                        </div>

                        <div class="form-grid" id="enhancedMitigationGroup" style="display: none;">
                            <div class="form-group">
                                <label for="benefitCostRatio">Estimated Benefit-Cost Ratio</label>
                                <input type="number" id="benefitCostRatio" name="benefitCostRatio"
                                       placeholder="0.00" step="0.01" min="0">
                            </div>

                            <div class="form-group">
                                <label for="mitigationCost">Additional Mitigation Cost</label>
                                <input type="number" id="mitigationCost" name="mitigationCost"
                                       placeholder="0.00" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <!-- EHP Compliance Section -->
                    <div style="background: #f0fdf4; border: 2px solid #22c55e; border-radius: 12px; padding: 20px; margin-bottom: 30px;">
                        <h4 style="color: #15803d; margin-bottom: 15px;">🌿 Environmental & Historic Preservation (EHP)</h4>

                        <div class="form-group">
                            <label for="facilityAge">Facility Age <span class="required">*</span></label>
                            <select id="facilityAge" name="facilityAge" required onchange="toggleHistoricReview()">
                                <option value="">Select facility age</option>
                                <option value="under45">Under 45 years old</option>
                                <option value="over45">45 years or older (Historic Review Required)</option>
                                <option value="unknown">Age Unknown</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="floodplainLocation">Is the project in a floodplain? <span class="required">*</span></label>
                            <select id="floodplainLocation" name="floodplainLocation" required onchange="toggleFloodplainReview()">
                                <option value="">Select floodplain status</option>
                                <option value="no">Not in Floodplain</option>
                                <option value="yes">In SFHA (Special Flood Hazard Area)</option>
                                <option value="unknown">Unknown - Requires Determination</option>
                            </select>
                        </div>

                        <div class="form-group" id="historicReviewGroup" style="display: none;">
                            <label for="historicStatus">Historic Preservation Status</label>
                            <select id="historicStatus" name="historicStatus">
                                <option value="">Select historic status</option>
                                <option value="listed">Listed on National Register</option>
                                <option value="eligible">Eligible for National Register</option>
                                <option value="district">In Historic District</option>
                                <option value="not-historic">Not Historic</option>
                                <option value="needs-review">Requires SHPO/THPO Review</option>
                            </select>
                        </div>

                        <div class="form-group" id="floodplainReviewGroup" style="display: none;">
                            <label for="firmPanel">FIRM Panel Number</label>
                            <input type="text" id="firmPanel" name="firmPanel"
                                   placeholder="FIRM panel number for floodplain determination">
                        </div>

                        <div class="form-group">
                            <label for="environmentalConcerns">Environmental Concerns</label>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-top: 8px;">
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="environmentalConcerns" value="wetlands" style="margin-right: 8px;">
                                    Wetlands Present
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="environmentalConcerns" value="endangered" style="margin-right: 8px;">
                                    Endangered Species
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="environmentalConcerns" value="coastal" style="margin-right: 8px;">
                                    Coastal Zone
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="environmentalConcerns" value="cultural" style="margin-right: 8px;">
                                    Cultural Resources
                                </label>
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-secondary" onclick="generateComplianceAnalysis()"
                            style="width: 100%; margin-top: 20px;">
                        🤖 Generate Automated Compliance Analysis
                    </button>
                </div>

                <!-- STEP 8: Procurement Compliance -->
                <div class="form-step" data-step="8">
                    <h3 style="color: #059669; margin-bottom: 20px;">⚖️ Procurement Compliance</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">All FEMA-funded projects must comply with 2 CFR 200 procurement requirements.</p>

                    <div style="background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 12px; padding: 20px; margin-bottom: 30px;">
                        <h4 style="color: #1d4ed8; margin-bottom: 15px;">📋 Procurement Requirements (2 CFR 200)</h4>

                        <div class="form-group">
                            <label for="procurementMethod">Procurement Method <span class="required">*</span></label>
                            <select id="procurementMethod" name="procurementMethod" required onchange="toggleProcurementDetails()">
                                <option value="">Select procurement method</option>
                                <option value="micro">Micro-Purchase (&lt; $10,000)</option>
                                <option value="small">Small Purchase ($10,000 - $250,000)</option>
                                <option value="sealed">Sealed Bids/IFB (&gt; $250,000)</option>
                                <option value="competitive">Competitive Proposals/RFP (&gt; $250,000)</option>
                                <option value="noncompetitive">Noncompetitive (Sole Source)</option>
                            </select>
                        </div>

                        <div class="form-group" id="competitiveGroup" style="display: none;">
                            <label for="bidderCount">Number of Bidders/Proposers</label>
                            <input type="number" id="bidderCount" name="bidderCount"
                                   placeholder="Number of responsive bids/proposals" min="1">
                        </div>

                        <div class="form-group" id="soleSourceGroup" style="display: none;">
                            <label for="soleSourceJustification">Sole Source Justification <span class="required">*</span></label>
                            <textarea id="soleSourceJustification" name="soleSourceJustification"
                                      placeholder="Provide detailed justification for noncompetitive procurement per 2 CFR 200.320(c)"
                                      style="min-height: 100px;"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="contractorInfo">Selected Contractor Information</label>
                            <div class="form-grid" style="margin-top: 10px;">
                                <input type="text" id="contractorName" name="contractorName"
                                       placeholder="Contractor name">
                                <input type="text" id="contractorLicense" name="contractorLicense"
                                       placeholder="License number">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="procurementCompliance">Procurement Compliance Checklist</label>
                            <div style="display: grid; grid-template-columns: 1fr; gap: 8px; margin-top: 8px;">
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="procurementCompliance" value="competition" style="margin-right: 8px;">
                                    Full and open competition conducted
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="procurementCompliance" value="specifications" style="margin-right: 8px;">
                                    Specifications avoid restrictive requirements
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="procurementCompliance" value="conflicts" style="margin-right: 8px;">
                                    No organizational conflicts of interest
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="procurementCompliance" value="documentation" style="margin-right: 8px;">
                                    Procurement documentation maintained
                                </label>
                                <label style="display: flex; align-items: center; font-weight: normal;">
                                    <input type="checkbox" name="procurementCompliance" value="debarment" style="margin-right: 8px;">
                                    Contractor debarment/suspension check completed
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="costReasonableness">Cost Reasonableness Analysis</label>
                        <textarea id="costReasonableness" name="costReasonableness"
                                  placeholder="Describe how costs were determined to be reasonable, including market analysis, cost comparisons, or independent estimates"
                                  style="min-height: 100px;"></textarea>
                    </div>
                </div>

                <!-- STEP 9: FEMA Project Workbook Template (Cost Capture & Analysis) -->
                <div class="form-step" data-step="9">
                    <h3 style="color: #059669; margin-bottom: 20px;">📋 FEMA Project Workbook Template</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">
                        Complete the official FEMA Project Workbook Template for cost capture and analysis. This replaces the traditional Form 90-91 and integrates with the compliance analysis from uploaded documents.
                    </p>

                    <!-- Cost Capture Trigger Check -->
                    <div style="background: #f0f9ff; border: 2px solid #3b82f6; border-radius: 12px; padding: 20px; margin-bottom: 30px;">
                        <h4 style="color: #1d4ed8; margin-bottom: 15px;">💰 Cost Analysis Required</h4>
                        <p style="margin-bottom: 15px;">Based on your project details, cost capture and analysis is required for:</p>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                            <div style="background: white; padding: 10px; border-radius: 6px; border-left: 4px solid #3b82f6;">
                                <strong>Project Category:</strong> <span id="workbook-category">Category C - Roads & Bridges</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px; border-left: 4px solid #3b82f6;">
                                <strong>Work Type:</strong> <span id="workbook-work-type">Permanent Work</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px; border-left: 4px solid #3b82f6;">
                                <strong>Estimated Cost:</strong> <span id="workbook-estimated-cost">$500,000</span>
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 6px; border-left: 4px solid #3b82f6;">
                                <strong>BCA Required:</strong> <span id="workbook-bca-required">No (Under $1M)</span>
                            </div>
                        </div>
                    </div>

                    <div id="fema-workbook-container">
                        <!-- FEMA Workbook will be dynamically inserted here -->
                    </div>




                    <!-- BCA Analysis (for projects over $1M) -->
                    <div id="bcaAnalysisSection" style="background: #f0fdf4; border: 2px solid #22c55e; border-radius: 12px; padding: 20px; margin-bottom: 30px; display: none;">
                        <h4 style="color: #15803d; margin-bottom: 15px;">📈 Benefit-Cost Analysis (FEMA BCA Toolkit 6.0)</h4>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="bcaMethod">BCA Analysis Method</label>
                                <select id="bcaMethod" name="bcaMethod">
                                    <option value="">Select BCA method</option>
                                    <option value="detailed">Detailed BCA (Toolkit 6.0)</option>
                                    <option value="simplified">Simplified BCA</option>
                                    <option value="qualitative">Qualitative Analysis</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="benefitCostRatio">Benefit-Cost Ratio</label>
                                <input type="number" id="benefitCostRatio" name="benefitCostRatio"
                                       placeholder="0.00" step="0.01" min="0">
                            </div>

                            <div class="form-group">
                                <label for="annualBenefits">Annual Benefits</label>
                                <input type="number" id="annualBenefits" name="annualBenefits"
                                       placeholder="0.00" step="0.01" min="0">
                            </div>

                            <div class="form-group">
                                <label for="projectLifespan">Project Lifespan (years)</label>
                                <input type="number" id="projectLifespan" name="projectLifespan"
                                       placeholder="50" min="1" max="100">
                            </div>
                        </div>

                        <button type="button" class="btn btn-secondary" onclick="openBCAToolkit()"
                                style="width: 100%; margin-top: 15px;">
                            📊 Open FEMA BCA Toolkit 6.0 Integration
                        </button>
                    </div>

                    <!-- Cost Reasonableness Documentation -->
                    <div style="background: #fef2f2; border: 2px solid #ef4444; border-radius: 12px; padding: 20px;">
                        <h4 style="color: #dc2626; margin-bottom: 15px;">📋 Cost Reasonableness Documentation</h4>

                        <div class="form-group">
                            <label for="costJustification">Cost Reasonableness Justification <span class="required">*</span></label>
                            <textarea id="costJustification" name="costJustification" required
                                      placeholder="Provide detailed justification for cost reasonableness including market analysis, cost comparisons, and supporting documentation"
                                      style="min-height: 120px;"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="costComparisons">Cost Comparison Sources</label>
                            <textarea id="costComparisons" name="costComparisons"
                                      placeholder="List specific cost comparison sources, similar projects, market rates, or independent estimates used"
                                      style="min-height: 80px;"></textarea>
                        </div>

                        <button type="button" class="btn btn-secondary" onclick="generateCostAnalysis()"
                                style="width: 100%; margin-top: 15px;">
                            🤖 Generate Automated Cost Reasonableness Analysis
                        </button>
                    </div>
                </div>

                <!-- STEP 10: Review & Submit -->
                <div class="form-step" data-step="10">
                    <h3 style="color: #059669; margin-bottom: 20px;">📋 Review & Submit</h3>
                    <p style="color: #6b7280; margin-bottom: 30px;">Please review your information before submitting:</p>

                    <div class="review-section">
                        <h4>Applicant Information</h4>
                        <div id="reviewApplicant"></div>

                        <h4>Project Details</h4>
                        <div id="reviewProject"></div>

                        <h4>Selected Category</h4>
                        <div id="reviewCategory"></div>
                    </div>

                    <div class="success-message" id="submitSuccess" style="display: none;">
                        <strong>✅ Submission Successful!</strong><br>
                        Your permanent work intake has been submitted. You will receive a confirmation email shortly.
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="navigation-buttons">
                    <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeStep(-1)" style="display: none;">Previous</button>
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">Next</button>
                </div>
            </form>
        </div>
    </div>
</main>

<div id="cmx-footer"></div>

<script>
let currentStep = 1;
const totalSteps = 10;
let uploadedFiles = [];
let selectedCBCSCodes = [];
let complianceData = {
    insurance: {},
    mitigation: {},
    ehp: {},
    procurement: {},
    costs: {}
};

// Cost analysis data structure
let costAnalysis = {
    labor: [],
    equipment: [],
    materials: [],
    contracts: [],
    insurance: [],
    other: [],
    bcaData: {},
    reasonableness: {}
};

// PAPAG Version mapping based on incident dates
const papagVersions = {
    'v1.0': { startDate: '2007-01-01', endDate: '2010-12-31', description: 'Original PAPAG (2007-2010)' },
    'v2.0': { startDate: '2011-01-01', endDate: '2013-12-31', description: 'PAPAG Version 2 (2011-2013)' },
    'v3.0': { startDate: '2014-01-01', endDate: '2016-12-31', description: 'PAPAG Version 3 (2014-2016)' },
    'v4.0': { startDate: '2017-01-01', endDate: '2020-12-31', description: 'PAPAG Version 4 (2017-2020)' },
    'v5.0': { startDate: '2021-01-01', endDate: '2030-12-31', description: 'Current PAPAG Version 5 (2021-present)' }
};

// Compliance pods data for Categories C-G
const compliancePods = {
    'C': {
        applicant: [
            '📸 Pre-disaster photos of roads/bridges',
            '🗺️ Engineering drawings and specifications',
            '📋 Damage assessment reports',
            '⚖️ Traffic count data and usage patterns',
            '🚛 Construction cost estimates',
            '📄 Environmental compliance documentation',
            '📍 Survey data and right-of-way information',
            '🔧 Materials testing and quality control records'
        ],
        system: [
            '📚 PAPAG Section III.C - Roads and Bridges',
            '🔍 CBCS codes for roadway construction',
            '📊 Cost reasonableness analysis',
            '🌿 Environmental and historic preservation review',
            '⚖️ Procurement compliance (2 CFR 200)',
            '📋 Design standards and specifications',
            '🛡️ Safety and traffic control requirements',
            '📈 Project delivery method analysis'
        ]
    },
    'D': {
        applicant: [
            '📸 Pre/post-disaster photos of water facilities',
            '🗺️ Hydraulic studies and flood modeling',
            '📋 Structural engineering assessments',
            '⚖️ Water flow and capacity data',
            '🚛 Repair/replacement cost estimates',
            '📄 Environmental impact assessments',
            '📍 Geotechnical and soil analysis',
            '🔧 Operations and maintenance records'
        ],
        system: [
            '📚 PAPAG Section III.D - Water Control Facilities',
            '🔍 CBCS codes for water infrastructure',
            '📊 Hydraulic design standards',
            '🌿 Environmental compliance (Clean Water Act)',
            '⚖️ Procurement and contracting requirements',
            '📋 Dam safety and inspection protocols',
            '🛡️ Flood control effectiveness analysis',
            '📈 Climate resilience considerations'
        ]
    },
    'E': {
        applicant: [
            '📸 Building damage documentation',
            '🗺️ Architectural and structural drawings',
            '📋 Professional engineering assessments',
            '⚖️ Equipment inventory and condition reports',
            '🚛 Construction and equipment cost estimates',
            '📄 Building code compliance documentation',
            '📍 Site surveys and utility connections',
            '🔧 Maintenance and inspection records'
        ],
        system: [
            '📚 PAPAG Section III.E - Buildings and Equipment',
            '🔍 CBCS codes for building construction',
            '📊 Cost estimation and value engineering',
            '🌿 Environmental and accessibility compliance',
            '⚖️ Procurement and competitive bidding',
            '📋 Building codes and safety standards',
            '🛡️ Seismic and wind resistance requirements',
            '📈 Energy efficiency and sustainability'
        ]
    },
    'F': {
        applicant: [
            '📸 Utility system damage documentation',
            '🗺️ Electrical, water, sewer system drawings',
            '📋 Utility engineering assessments',
            '⚖️ Service capacity and load analysis',
            '🚛 Infrastructure repair cost estimates',
            '📄 Utility regulatory compliance records',
            '📍 Right-of-way and easement documentation',
            '🔧 System operations and maintenance logs'
        ],
        system: [
            '📚 PAPAG Section III.F - Utilities',
            '🔍 CBCS codes for utility infrastructure',
            '📊 Utility design and capacity standards',
            '🌿 Environmental compliance (multiple acts)',
            '⚖️ Procurement and utility regulations',
            '📋 Safety codes and operational standards',
            '🛡️ System redundancy and reliability',
            '📈 Smart grid and modernization options'
        ]
    },
    'G': {
        applicant: [
            '📸 Recreation facility damage photos',
            '🗺️ Site plans and facility drawings',
            '📋 Structural and landscape assessments',
            '⚖️ Usage data and community impact analysis',
            '🚛 Restoration and improvement cost estimates',
            '📄 ADA compliance documentation',
            '📍 Environmental and cultural resource surveys',
            '🔧 Maintenance and safety inspection records'
        ],
        system: [
            '📚 PAPAG Section III.G - Parks and Recreation',
            '🔍 CBCS codes for recreational facilities',
            '📊 Community benefit and usage analysis',
            '🌿 Environmental and cultural preservation',
            '⚖️ Procurement and public contracting',
            '📋 Safety and accessibility standards',
            '🛡️ Risk management and liability',
            '📈 Community resilience and social benefits'
        ]
    }
};

function determinePapagVersion(incidentDate) {
    const date = new Date(incidentDate);
    
    for (const [version, info] of Object.entries(papagVersions)) {
        const start = new Date(info.startDate);
        const end = new Date(info.endDate);
        
        if (date >= start && date <= end) {
            return { version, ...info };
        }
    }
    
    // Default to current version if date is in future
    return { version: 'v5.0', ...papagVersions['v5.0'] };
}

function updatePapagInfo() {
    const incidentDate = document.getElementById('incidentDate').value;
    const papagInfo = document.getElementById('papagInfo');
    
    if (incidentDate) {
        const papag = determinePapagVersion(incidentDate);
        document.getElementById('papagVersion').textContent = `PAPAG ${papag.version} applies to this incident`;
        document.getElementById('papagDetails').textContent = papag.description;
        papagInfo.classList.add('show');
    } else {
        papagInfo.classList.remove('show');
    }
}

function populateCompliancePods(category) {
    const applicantDocs = document.getElementById('applicantDocs');
    const systemDocs = document.getElementById('systemDocs');

    if (compliancePods[category]) {
        applicantDocs.innerHTML = compliancePods[category].applicant
            .map(doc => `<li>${doc}</li>`).join('');
        systemDocs.innerHTML = compliancePods[category].system
            .map(doc => `<li>${doc}</li>`).join('');
    }
}

function validateStep(step) {
    const stepElement = document.querySelector(`.form-step[data-step="${step}"]`);
    const requiredFields = stepElement.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.style.borderColor = '#dc2626';
            isValid = false;
        } else {
            field.style.borderColor = '#e5e7eb';
        }
    });

    // Additional validation for step 1
    if (step === 1) {
        const email = document.getElementById('contactEmail');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email.value && !emailRegex.test(email.value)) {
            email.style.borderColor = '#dc2626';
            isValid = false;
        }

        const phone = document.getElementById('contactPhone');
        const phoneRegex = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
        if (phone.value && !phoneRegex.test(phone.value)) {
            phone.style.borderColor = '#dc2626';
            isValid = false;
        }
    }

    // Additional validation for step 2
    if (step === 2) {
        const category = document.getElementById('workCategory').value;
        if (!category) {
            document.getElementById('categoryError').style.display = 'block';
            isValid = false;
        } else {
            document.getElementById('categoryError').style.display = 'none';
        }
    }

    // Additional validation for step 7 (Mitigation & EHP)
    if (step === 7) {
        const hazardTypes = document.querySelectorAll('input[name="hazardTypes"]:checked');
        if (hazardTypes.length === 0) {
            isValid = false;
            // Could add specific error messaging here
        }
    }

    // Additional validation for step 8 (Procurement)
    if (step === 8) {
        const method = document.getElementById('procurementMethod').value;
        if (method === 'noncompetitive') {
            const justification = document.getElementById('soleSourceJustification').value;
            if (!justification.trim()) {
                document.getElementById('soleSourceJustification').style.borderColor = '#dc2626';
                isValid = false;
            }
        }
    }

    return isValid;
}

function populateReviewSection() {
    const reviewApplicant = document.getElementById('reviewApplicant');
    const reviewProject = document.getElementById('reviewProject');
    const reviewCategory = document.getElementById('reviewCategory');

    // Applicant information
    reviewApplicant.innerHTML = `
        <div class="review-item"><span class="review-label">Name:</span> <span class="review-value">${document.getElementById('applicantName').value}</span></div>
        <div class="review-item"><span class="review-label">Type:</span> <span class="review-value">${document.getElementById('applicantType').value}</span></div>
        <div class="review-item"><span class="review-label">Contact:</span> <span class="review-value">${document.getElementById('contactPerson').value}</span></div>
        <div class="review-item"><span class="review-label">Email:</span> <span class="review-value">${document.getElementById('contactEmail').value}</span></div>
        <div class="review-item"><span class="review-label">Phone:</span> <span class="review-value">${document.getElementById('contactPhone').value}</span></div>
        <div class="review-item"><span class="review-label">DR#:</span> <span class="review-value">${document.getElementById('disasterNumber').value || 'Not provided'}</span></div>
    `;

    // Project information
    const incidentDate = document.getElementById('incidentDate').value;
    const papag = determinePapagVersion(incidentDate);
    reviewProject.innerHTML = `
        <div class="review-item"><span class="review-label">Title:</span> <span class="review-value">${document.getElementById('projectTitle').value}</span></div>
        <div class="review-item"><span class="review-label">Location:</span> <span class="review-value">${document.getElementById('projectLocation').value}</span></div>
        <div class="review-item"><span class="review-label">Incident Date:</span> <span class="review-value">${incidentDate}</span></div>
        <div class="review-item"><span class="review-label">PAPAG Version:</span> <span class="review-value">${papag.version} - ${papag.description}</span></div>
        <div class="review-item"><span class="review-label">Estimated Cost:</span> <span class="review-value">$${document.getElementById('estimatedCost').value || '0.00'}</span></div>
    `;

    // Category information
    const category = document.getElementById('workCategory').value;
    const categoryNames = {
        'C': 'Category C - Roads & Bridges',
        'D': 'Category D - Water Control Facilities',
        'E': 'Category E - Buildings & Equipment',
        'F': 'Category F - Utilities',
        'G': 'Category G - Parks & Recreation'
    };

    // Insurance information
    const hasInsurance = document.getElementById('hasInsurance').value;
    const insuranceStatus = document.getElementById('insuranceStatus').value;
    const settlementAmount = document.getElementById('settlementAmount').value;

    reviewCategory.innerHTML = `
        <div class="review-item"><span class="review-label">Category:</span> <span class="review-value">${categoryNames[category] || 'Not selected'}</span></div>

        <h4>Insurance & DOB</h4>
        <div class="review-item"><span class="review-label">Insurance:</span> <span class="review-value">${hasInsurance || 'Not specified'}</span></div>
        <div class="review-item"><span class="review-label">Claim Status:</span> <span class="review-value">${insuranceStatus || 'Not specified'}</span></div>
        ${settlementAmount ? `<div class="review-item"><span class="review-label">Settlement:</span> <span class="review-value">$${settlementAmount}</span></div>` : ''}

        <h4>Compliance Status</h4>
        <div class="review-item"><span class="review-label">Mitigation Required:</span> <span class="review-value">${document.getElementById('mitigationRequired')?.value || 'Not specified'}</span></div>
        <div class="review-item"><span class="review-label">Facility Age:</span> <span class="review-value">${document.getElementById('facilityAge')?.value || 'Not specified'}</span></div>
        <div class="review-item"><span class="review-label">Floodplain:</span> <span class="review-value">${document.getElementById('floodplainLocation')?.value || 'Not specified'}</span></div>
        <div class="review-item"><span class="review-label">Procurement Method:</span> <span class="review-value">${document.getElementById('procurementMethod')?.value || 'Not specified'}</span></div>

        <h4>Cost Analysis</h4>
        <div class="review-item"><span class="review-label">Cost Method:</span> <span class="review-value">${document.getElementById('costEstimationMethod')?.value || 'Not specified'}</span></div>
        <div class="review-item"><span class="review-label">Total Project Cost:</span> <span class="review-value">${document.getElementById('totalProjectCost')?.textContent || '$0.00'}</span></div>
        <div class="review-item"><span class="review-label">BCA Required:</span> <span class="review-value">${document.getElementById('bcaAnalysisSection')?.style.display === 'block' ? 'Yes (>$1M)' : 'No (<$1M)'}</span></div>
    `;
}

function changeStep(direction) {
    if (direction === 1 && !validateStep(currentStep)) {
        return;
    }

    // Handle submission on final step
    if (currentStep === totalSteps && direction === 1) {
        submitForm();
        return;
    }

    const newStep = currentStep + direction;

    if (newStep < 1 || newStep > totalSteps) {
        return;
    }

    // Hide current step
    document.querySelector(`[data-step="${currentStep}"].form-step`).classList.remove('active');
    document.querySelector(`[data-step="${currentStep}"].step`).classList.remove('active');

    // Mark completed steps
    if (direction === 1) {
        document.querySelector(`[data-step="${currentStep}"].step`).classList.add('completed');
    }

    // Show new step
    currentStep = newStep;
    document.querySelector(`[data-step="${currentStep}"].form-step`).classList.add('active');
    document.querySelector(`[data-step="${currentStep}"].step`).classList.add('active');

    // Special handling for step 9 (FEMA Workbook)
    if (currentStep === 9) {
        initializeFEMAWorkbook();
    }

    // Special handling for step 10 (review)
    if (currentStep === 10) {
        populateReviewSection();
    }

    // Update navigation buttons
    document.getElementById('prevBtn').style.display = currentStep === 1 ? 'none' : 'inline-block';
    document.getElementById('nextBtn').textContent = currentStep === totalSteps ? 'Submit' : 'Next';

    // Update page data attribute for tracking
    document.body.setAttribute('data-step', currentStep);
}

function submitForm() {
    // Show success message
    document.getElementById('submitSuccess').style.display = 'block';
    document.getElementById('nextBtn').disabled = true;
    document.getElementById('nextBtn').textContent = 'Submitted';

    // Here you would normally send the data to your backend
    console.log('Form submitted with data:', {
        applicant: {
            name: document.getElementById('applicantName').value,
            type: document.getElementById('applicantType').value,
            contact: document.getElementById('contactPerson').value,
            email: document.getElementById('contactEmail').value,
            phone: document.getElementById('contactPhone').value,
            address: document.getElementById('applicantAddress').value,
            drNumber: document.getElementById('disasterNumber').value
        },
        project: {
            title: document.getElementById('projectTitle').value,
            location: document.getElementById('projectLocation').value,
            incidentDate: document.getElementById('incidentDate').value,
            category: document.getElementById('workCategory').value,
            estimatedCost: document.getElementById('estimatedCost').value,
            damageDescription: document.getElementById('damageDescription').value,
            additionalNotes: document.getElementById('additionalNotes').value
        },
        papagVersion: determinePapagVersion(document.getElementById('incidentDate').value)
    });
}

// Insurance and compliance functions
function toggleInsuranceDetails() {
    const hasInsurance = document.getElementById('hasInsurance').value;
    const typeGroup = document.getElementById('insuranceTypeGroup');
    const detailsGroup = document.getElementById('insuranceDetailsGroup');

    if (hasInsurance === 'yes' || hasInsurance === 'partial') {
        typeGroup.style.display = 'block';
        detailsGroup.style.display = 'block';
    } else {
        typeGroup.style.display = 'none';
        detailsGroup.style.display = 'none';
    }
}

function toggleSettlementDetails() {
    const status = document.getElementById('insuranceStatus').value;
    const settlementGroup = document.getElementById('settlementDetailsGroup');

    if (status === 'partial' || status === 'full') {
        settlementGroup.style.display = 'block';
    } else {
        settlementGroup.style.display = 'none';
    }
}

function toggleMitigationDetails() {
    const required = document.getElementById('mitigationRequired').value;
    const enhancedGroup = document.getElementById('enhancedMitigationGroup');

    if (required === 'over1m') {
        enhancedGroup.style.display = 'block';
    } else {
        enhancedGroup.style.display = 'none';
    }
}

function toggleHistoricReview() {
    const age = document.getElementById('facilityAge').value;
    const historicGroup = document.getElementById('historicReviewGroup');

    if (age === 'over45' || age === 'unknown') {
        historicGroup.style.display = 'block';
    } else {
        historicGroup.style.display = 'none';
    }
}

function toggleFloodplainReview() {
    const location = document.getElementById('floodplainLocation').value;
    const floodplainGroup = document.getElementById('floodplainReviewGroup');

    if (location === 'yes' || location === 'unknown') {
        floodplainGroup.style.display = 'block';
    } else {
        floodplainGroup.style.display = 'none';
    }
}

function toggleProcurementDetails() {
    const method = document.getElementById('procurementMethod').value;
    const competitiveGroup = document.getElementById('competitiveGroup');
    const soleSourceGroup = document.getElementById('soleSourceGroup');

    if (method === 'sealed' || method === 'competitive') {
        competitiveGroup.style.display = 'block';
        soleSourceGroup.style.display = 'none';
    } else if (method === 'noncompetitive') {
        competitiveGroup.style.display = 'none';
        soleSourceGroup.style.display = 'block';
    } else {
        competitiveGroup.style.display = 'none';
        soleSourceGroup.style.display = 'none';
    }
}

function generateComplianceAnalysis() {
    alert('🤖 Generating Automated Compliance Analysis\n\nAnalyzing:\n• Section 406 mitigation requirements\n• EHP compliance checklist\n• Risk assessment\n• Recommended actions\n• Timeline and coordination\n\nEstimated completion: 2-3 minutes');
}

// Cost analysis functions
function toggleCostMethodDetails() {
    const method = document.getElementById('costEstimationMethod').value;
    // Could add method-specific fields here
}

function updateCostTotals() {
    const labor = parseFloat(document.getElementById('laborCosts').value) || 0;
    const equipment = parseFloat(document.getElementById('equipmentCosts').value) || 0;
    const materials = parseFloat(document.getElementById('materialsCosts').value) || 0;
    const other = parseFloat(document.getElementById('otherCosts').value) || 0;

    const subtotal = labor + equipment + materials + other;
    const contingency = subtotal * 0.10;
    const total = subtotal + contingency;

    document.getElementById('costSubtotal').textContent = '$' + subtotal.toLocaleString('en-US', {minimumFractionDigits: 2});
    document.getElementById('costContingency').textContent = '$' + contingency.toLocaleString('en-US', {minimumFractionDigits: 2});
    document.getElementById('totalProjectCost').textContent = '$' + total.toLocaleString('en-US', {minimumFractionDigits: 2});

    // Show BCA section if over $1M
    const bcaSection = document.getElementById('bcaAnalysisSection');
    if (total >= 1000000) {
        bcaSection.style.display = 'block';
    } else {
        bcaSection.style.display = 'none';
    }

    // Update cost analysis data
    costAnalysis.labor = [{description: 'Total Labor', amount: labor}];
    costAnalysis.equipment = [{description: 'Total Equipment', amount: equipment}];
    costAnalysis.materials = [{description: 'Total Materials', amount: materials}];
    costAnalysis.other = [{description: 'Other Costs', amount: other}];
}

function calculateLaborRate() {
    const laborCost = parseFloat(document.getElementById('laborCosts').value) || 0;
    const laborHours = parseFloat(document.getElementById('laborHours').value) || 0;

    if (laborHours > 0) {
        const rate = laborCost / laborHours;
        // Could display calculated rate
    }
}

function openBCAToolkit() {
    alert('🔗 Opening FEMA BCA Toolkit 6.0 Integration\n\nThis would open:\n• FEMA BCA Toolkit 6.0 Excel workbook\n• Pre-populated project data\n• Automated benefit calculations\n• Risk assessment tools\n• Compliance verification\n\nIntegration with local Excel installation...');
}

function generateCostAnalysis() {
    alert('🤖 Generating Automated Cost Reasonableness Analysis\n\nAnalyzing:\n• Market rate comparisons\n• Historical project data\n• Regional cost factors\n• Industry benchmarks\n• Cost escalation factors\n• Reasonableness assessment\n\nEstimated completion: 3-5 minutes');
}

// Event listeners
document.getElementById('incidentDate').addEventListener('change', updatePapagInfo);

// Category selection
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            // Remove selection from all category cards
            document.querySelectorAll('.category-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');

            const category = this.dataset.category;
            document.getElementById('workCategory').value = category;

            // Populate compliance pods for step 3
            populateCompliancePods(category);

            // Clear any error message
            document.getElementById('categoryError').style.display = 'none';
        });
    });

    // Initialize FEMA Workbook Integration
    if (currentStep === 9) {
        initializeFEMAWorkbook();
    }

    console.log('Permanent Work Intake loaded');
});

// Initialize FEMA Workbook when reaching step 9
function initializeFEMAWorkbook() {
    const container = document.getElementById('fema-workbook-container');
    if (container && femaWorkbook) {
        container.innerHTML = femaWorkbook.generateWorkbookHTML();

        // Populate with existing wizard data
        const wizardData = {
            applicantName: document.getElementById('applicantName')?.value,
            projectTitle: document.getElementById('projectTitle')?.value,
            category: getCategoryName(document.getElementById('workCategory')?.value),
            papagVersion: document.getElementById('papagVersion')?.textContent,
            drNumber: document.getElementById('drNumber')?.value,
            incidentDate: document.getElementById('incidentDate')?.value,
            projectLocation: document.getElementById('projectLocation')?.value,
            damageDescription: document.getElementById('damageDescription')?.value,
            insuranceCoverage: document.getElementById('hasInsurance')?.value,
            settlementAmount: document.getElementById('settlementAmount')?.value,
            otherAssistance: document.getElementById('otherAssistance')?.value
        };

        femaWorkbook.populateWorkbook(wizardData);

        // Set first tab as active
        switchWorkbookTab('project-info');
    }
}

function getCategoryName(categoryCode) {
    const categoryNames = {
        'C': 'Category C - Roads & Bridges',
        'D': 'Category D - Water Control Facilities',
        'E': 'Category E - Buildings & Equipment',
        'F': 'Category F - Utilities',
        'G': 'Category G - Parks & Recreation'
    };
    return categoryNames[categoryCode] || categoryCode;
}

// Sign out function
function signOut() {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userName');
    localStorage.removeItem('userOrganization');
    localStorage.removeItem('userSubscription');
    window.location.href = 'landing_page.html';
}

// Check authentication on page load
document.addEventListener('DOMContentLoaded', function() {
    const isAuthenticated = localStorage.getItem('isAuthenticated');
    if (!isAuthenticated || isAuthenticated !== 'true') {
        window.location.href = 'landing_page.html';
        return;
    }
    console.log('✅ Permanent Work Intake loaded - User authenticated');
});
</script>
</main>
<div id="cmx-footer"></div>
</body>
</html>
