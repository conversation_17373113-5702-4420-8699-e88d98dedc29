<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects - ComplianceMax</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <link rel="stylesheet" href="print.css" media="print">
    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #253464 0%, #1e2a5a 100%);
            color: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 40px;
        }

        .logo {
            font-size: 1.8em;
            font-weight: bold;
            color: #ffc107;
        }

        .main-nav {
            display: flex;
            align-items: center;
            gap: 24px;
            flex: 1;
            justify-content: center;
        }

        .nav-item {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            white-space: nowrap;
        }

        .nav-item:hover {
            color: white;
            background: rgba(255,255,255,0.1);
        }

        .nav-item.active {
            color: #ffc107;
            background: rgba(255,193,7,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .subscription-badge {
            background: #3b82f6;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .subscription-badge.basic {
            background: #6b7280;
        }

        .subscription-badge.professional {
            background: #3b82f6;
        }

        .subscription-badge.enterprise {
            background: #7c3aed;
        }

        .sign-out-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sign-out-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .page-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title h1 {
            font-size: 2.5rem;
            color: #253464;
            margin-bottom: 16px;
        }

        .page-title p {
            font-size: 1.2rem;
            color: #666;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 24px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 32px 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 16px;
            display: block;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #253464;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }

        .projects-section {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 1.8rem;
            font-weight: bold;
            color: #253464;
        }

        .new-project-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .new-project-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }

        .project-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .project-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .project-id {
            font-weight: bold;
            font-size: 1.1rem;
            color: #253464;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #cce5ff;
            color: #004085;
        }

        .project-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .project-description {
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .project-progress {
            margin-bottom: 16px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: #666;
        }

        .progress-bar {
            background: #e5e7eb;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .project-actions {
            display: flex;
            gap: 12px;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn.primary {
            background: #667eea;
            color: white;
        }

        .action-btn.primary:hover {
            background: #5a67d8;
        }

        .action-btn.secondary {
            background: #e5e7eb;
            color: #374151;
        }

        .action-btn.secondary:hover {
            background: #d1d5db;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .header-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .main-nav {
                order: -1;
                width: 100%;
                justify-content: flex-start;
                overflow-x: auto;
                padding: 0 0 16px 0;
            }
            
            .projects-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 640px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="universal-nav" class="site-nav"></div>
    <main id="main" class="container">
        <!-- Header -->
        <div class="header">
        <div class="header-content">
            <div class="logo">🎯 ComplianceMax</div>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-item">Dashboard</a>
                <a href="projects.html" class="nav-item active">Projects</a>
                <a href="compliance_workflow.html" class="nav-item">Compliance Workflow</a>
                <a href="cbcs_demo.html" class="nav-item">Professional Intake</a>
                <a href="emergency_intake.html" class="nav-item">Emergency Intake</a>
                <a href="worksheet.html" class="nav-item">Worksheet</a>
                <a href="document_upload_system.html" class="nav-item">Documents</a>
            </nav>
            <div class="user-info">
                <span id="welcomeMessage">Welcome, User</span>
                <div class="user-actions">
                    <span id="subscriptionBadge" class="subscription-badge">Professional</span>
                    <button onclick="signOut()" class="sign-out-btn">Sign Out</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <div class="page-title">
            <h1>Project Summary</h1>
            <p>Manage your Federal PA compliance projects</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">📊</span>
                <div class="stat-value">24</div>
                <div class="stat-label">Total Projects</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">⚡</span>
                <div class="stat-value">8</div>
                <div class="stat-label">Active Projects</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value">16</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-value">$12.5M</div>
                <div class="stat-label">Total Value</div>
            </div>
        </div>

        <!-- Projects Section -->
        <div class="projects-section">
            <div class="section-header">
                <h2 class="section-title">📋 Your Projects</h2>
                <a href="dashboard.html" class="new-project-btn">+ New Project</a>
            </div>

            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-header">
                        <span class="project-id">PW-001-2024</span>
                        <span class="status-badge status-active">Active</span>
                    </div>
                    <div class="project-title">Main Street Bridge Repair</div>
                    <div class="project-description">
                        Storm damage from Hurricane Delta - Emergency repairs and permanent restoration
                    </div>
                    <div class="project-progress">
                        <div class="progress-label">
                            <span>Progress</span>
                            <span>75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%;"></div>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="cbcs_demo.html" class="action-btn primary">📝 Edit</a>
                        <a href="report.html" class="action-btn secondary">📊 Report</a>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-header">
                        <span class="project-id">PW-003-2024</span>
                        <span class="status-badge status-pending">Pending Review</span>
                    </div>
                    <div class="project-title">Water Treatment Plant</div>
                    <div class="project-description">
                        Flood damage assessment and equipment replacement
                    </div>
                    <div class="project-progress">
                        <div class="progress-label">
                            <span>Progress</span>
                            <span>45%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%;"></div>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="cbcs_demo.html" class="action-btn primary">📝 Edit</a>
                        <a href="compliance_workflow.html" class="action-btn secondary">🔍 Review</a>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-header">
                        <span class="project-id">EM-005-2024</span>
                        <span class="status-badge status-active">Active</span>
                    </div>
                    <div class="project-title">Emergency Debris Removal</div>
                    <div class="project-description">
                        Downtown area debris removal and cleanup operations
                    </div>
                    <div class="project-progress">
                        <div class="progress-label">
                            <span>Progress</span>
                            <span>90%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%;"></div>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="emergency_intake.html" class="action-btn primary">📝 Edit</a>
                        <a href="report.html" class="action-btn secondary">📊 Report</a>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-header">
                        <span class="project-id">PW-007-2024</span>
                        <span class="status-badge status-completed">Completed</span>
                    </div>
                    <div class="project-title">City Hall Roof Replacement</div>
                    <div class="project-description">
                        Wind damage repair and roof replacement with upgraded materials
                    </div>
                    <div class="project-progress">
                        <div class="progress-label">
                            <span>Progress</span>
                            <span>100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%;"></div>
                        </div>
                    </div>
                    <div class="project-actions">
                        <a href="report.html" class="action-btn primary">📊 View Report</a>
                        <a href="packet.html" class="action-btn secondary">📄 Download</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Authentication check
        function checkAuthentication() {
            const isAuthenticated = localStorage.getItem('isAuthenticated');
            if (!isAuthenticated || isAuthenticated !== 'true') {
                window.location.href = 'landing_page.html';
                return false;
            }
            return true;
        }

        // Load user information
        function loadUserInfo() {
            const userName = localStorage.getItem('userName') || 'User';
            const userSubscription = localStorage.getItem('userSubscription') || 'professional';
            
            document.getElementById('welcomeMessage').textContent = `Welcome, ${userName}`;
            
            const subscriptionBadge = document.getElementById('subscriptionBadge');
            subscriptionBadge.textContent = userSubscription.charAt(0).toUpperCase() + userSubscription.slice(1);
            subscriptionBadge.className = `subscription-badge ${userSubscription}`;
        }

        // Sign out function
        function signOut() {
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('userEmail');
            localStorage.removeItem('userName');
            localStorage.removeItem('userOrganization');
            localStorage.removeItem('userSubscription');
            window.location.href = 'landing_page.html';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkAuthentication()) return;
            loadUserInfo();
            console.log('✅ Projects page loaded - User authenticated');
        });
    </script>
    </main>
    <div id="cmx-footer"></div>
</body>
</html>
