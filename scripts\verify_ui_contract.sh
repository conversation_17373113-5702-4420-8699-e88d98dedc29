#!/usr/bin/env sh
set -eu
fail=0
ROOT_HTML="$(find . -maxdepth 1 -type f -name '*.html' | sort)"

exists() { [ -f "$1" ]; }

check_head_order() {
  file="$1"
  head=$(awk '/<head[^>]*>/,/<\/head>/' "$file" | tr -d '\r')
  navcss_ok=$(printf '%s' "$head" | grep -Eq '<link[^>]+href="ui/nav\.css"'; echo $?)
  stack_present=$(exists ./ui/stack.css && printf '%s' "$head" | grep -Eq '<link[^>]+href="ui/stack\.css"'; echo $? || true)
  navjs_ok=$(printf '%s' "$head" | grep -Eq '<script[^>]+src="nav\.js"[^>]+defer'; echo $?)
  footerjs_present=$(exists ./footer.js && printf '%s' "$head" | grep -Eq '<script[^>]+src="footer\.js"[^>]+defer'; echo $? || true)

  order_ok=0
  if [ "$navcss_ok" -eq 0 ]; then
    if [ "$stack_present" -eq 0 ]; then
      nav_line=$(printf '%s' "$head" | nl -ba | grep 'href="ui/nav\.css"'   | head -1 | awk '{print $1}')
      stk_line=$(printf '%s' "$head" | nl -ba | grep 'href="ui/stack\.css"' | head -1 | awk '{print $1}')
      [ -n "${nav_line:-}" ] && [ -n "${stk_line:-}" ] && [ "$nav_line" -lt "$stk_line" ] && order_ok=1 || order_ok=0
    else
      order_ok=1
    fi
  fi

  no_footer_css=$(printf '%s' "$head" | grep -Eq 'href="ui/footer\.css"' && echo 1 || echo 0)

  head_ok=1; note=""
  [ "$navcss_ok" -eq 0 ] || { head_ok=0; note="$note missing ui/nav.css;"; }
  if exists ./ui/stack.css && [ "$stack_present" -ne 0 ]; then head_ok=0; note="$note missing ui/stack.css ref;"; fi
  [ "$order_ok" -eq 1 ] || { head_ok=0; note="$note wrong css order;"; }
  [ "$navjs_ok" -eq 0 ] || { head_ok=0; note="$note missing nav.js defer;"; }
  if exists ./footer.js && [ "$footerjs_present" -ne 0 ]; then head_ok=0; note="$note missing footer.js defer;"; fi
  [ "$no_footer_css" -eq 0 ] || { head_ok=0; note="$note has deprecated ui/footer.css;"; }

  printf '%s|%s\n' "$head_ok" "$(printf '%s' "$note" | sed 's/;*$//')"
}

check_body_shell() {
  file="$1"
  u=$(grep -n '<div id="universal-nav"' "$file" 2>/dev/null | wc -l | tr -d ' ')
  m=$(grep -n '<main id="main"'          "$file" 2>/dev/null | wc -l | tr -d ' ')
  f=$(grep -n '<div id="cmx-footer"'     "$file" 2>/dev/null | wc -l | tr -d ' ')
  bdisp=$(grep -E -n '<body[^>]*display\s*:\s*(flex|grid)' "$file" 2>/dev/null | wc -l | tr -d ' ')
  ok=1; note=""
  [ "$u" -ge 1 ] || { ok=0; note="$note missing universal-nav;"; }
  [ "$m" -ge 1 ] || { ok=0; note="$note missing main;"; }
  [ "$f" -ge 1 ] || { ok=0; note="$note missing footer mount;"; }
  [ "$bdisp" -eq 0 ] || { ok=0; note="$note body has display property;"; }
  printf '%s|%s\n' "$ok" "$(printf '%s' "$note" | sed 's/;*$//')"
}

has_sidewalk() { grep -Eq '\.sidewalk' "$1" && echo 1 || echo 0; }
has_hints() { grep -Eq 'sidewalk-hints\.css|sidewalk-hints\.js' "$1" && echo 1 || echo 0; }
has_footer_css() { grep -Eq 'href="ui/footer\.css"' "$1" && echo 1 || echo 0; }

echo "file,head_ok,body_shell_ok,has_sidewalk,has_hints_includes,has_footer_css_include,notes"
for f in $ROOT_HTML; do
  case "$f" in ./index.html) echo "index.html,N/A,N/A,0,0,0,redirect page - excluded"; continue;; esac
  ho="$(check_head_order "$f")"; bo="$(check_body_shell "$f")"
  hs="$(has_sidewalk "$f")"; hh="$(has_hints "$f")"; hf="$(has_footer_css "$f")"
  head_ok="${ho%%|*}"; head_note="${ho#*|}"; body_ok="${bo%%|*}"; body_note="${bo#*|}"
  if [ "$hs" -eq 0 ] && [ "$hh" -eq 1 ]; then head_ok=0; head_note="$(printf '%s; hints w/o .sidewalk' "$head_note")"; fi
  [ "$head_ok" -eq 1 ] || fail=1; [ "$body_ok" -eq 1 ] || fail=1; [ "$hf" -eq 0 ] || fail=1
  notes="$(printf '%s %s' "$head_note" "$body_note" | sed 's/^ *//;s/ *$//')"
  echo "$(basename "$f"),$([ "$head_ok" -eq 1 ] && echo true || echo false),$([ "$body_ok" -eq 1 ] && echo true || echo false),$([ "$hs" -eq 1 ] && echo true || echo false),$([ "$hh" -eq 1 ] && echo true || echo false),$([ "$hf" -eq 1 ] && echo true || echo false),${notes:-ok}"
done
exit $fail
