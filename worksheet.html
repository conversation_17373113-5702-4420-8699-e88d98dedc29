<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Worksheet — ComplianceMax</title>
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" href="ui/nav.css">
<link rel="stylesheet" href="ui/stack.css">
<!-- <link rel="stylesheet" href="ui/sidewalk.css"> QUARANTINED: moved to legacy/sidewalk/ -->
<link rel="stylesheet" href="print.css" media="print">

<script src="nav.js" defer></script>
<script src="footer.js" defer></script>
<style>
  body{font-family:system-ui,Segoe UI,Arial; margin:24px;}
  nav a{margin-right:8px}
  table{width:100%; border-collapse:collapse; margin:12px 0}
  th,td{border:1px solid #ddd; padding:8px}
  th{background:#f6f6f6; text-align:left}
  .pill{display:inline-block;padding:2px 8px;border-radius:999px;background:#eef}
  .row{display:flex;gap:12px;flex-wrap:wrap}
  .card{flex:1 1 320px;border:1px solid #ddd;border-radius:12px;padding:12px}
</style>
</head>
<body data-page="worksheet" data-flow="professional" data-step="5">
<div id="universal-nav"></div>
<!-- QUARANTINED: sidewalk navigation removed -->
<!-- <div class="sidewalk-wrap"><div id="sidewalk"></div></div> -->
<!-- <script>document.documentElement.classList.add('has-sidewalk');</script> -->

<main id="main" class="container">
<h1>Worksheet</h1>
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
  <div id="meta" style="color:#555"></div>
  <label style="margin:4px 0;">
    Region:
    <select id="cmx-region" style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 4px;">
      <option value="default">Default (1.00)</option>
      <option value="US-South">US South (0.96)</option>
      <option value="US-Northeast">US Northeast (1.08)</option>
      <option value="US-West">US West (1.05)</option>
      <option value="US-Midwest">US Midwest (0.99)</option>
    </select>
  </label>
</div>

<div class="row" id="buckets"></div>

<div id="cost-source-summary" style="margin-top:10px;background:#f8fafc;border:1px solid #e5e7eb;padding:15px;border-radius:8px;">
  <h4>📊 Cost Sources Summary</h4>
  <em>Add cost items to see source breakdown</em>
</div>

<div style="display:grid;grid-template-columns:1fr 1fr auto;gap:20px;margin-top:20px;align-items:center;">
  <div>
    <button id="export-evidence" class="no-print" style="background:#667eea;color:white;border:none;padding:10px 20px;border-radius:20px;cursor:pointer;font-weight:500;">
      📋 Export Evidence Manifest (.json + .md)
    </button>
  </div>
  <div>
    <button class="no-print" onclick="window.print()" style="background:#28a745;color:white;border:none;padding:10px 20px;border-radius:20px;cursor:pointer;font-weight:500;">
      📄 Download Worksheet PDF
    </button>
  </div>
  <div style="text-align:right;padding:15px;background:#f8f9fa;border-radius:10px;font-size:1.2rem;border:2px solid #28a745;">
    <strong>Grand Total: <span id="grand-total" style="color:#28a745;">$0.00</span></strong>
  </div>
</div>

<div style="margin-top:12px;display:flex;gap:8px;flex-wrap:wrap">
  <button id="save">Save</button>
  <button id="export">Export JSON</button>
  <button id="toReport" onclick="location.href='report.html'">Build Report</button>
</div>

<script>
const CMX_COST_FACTORS = {
  // region multipliers (example values you can tailor)
  regionFactors: {
    default: 1.00,
    'US-South': 0.96,
    'US-Northeast': 1.08,
    'US-West': 1.05,
    'US-Midwest': 0.99
  },
  // edition/quarter factor (inflation/updates), set per source if desired
  edition: {
    RSMeans: { year: 2025, factor: 1.00 },
    NationalConstrEstimator: { year: 2025, factor: 1.00 },
    FEMA_EquipRates: { year: 2024, factor: 1.00 }
  }
};

(function(){
  const K='ComplianceMax_Demo:worksheet';
  const ls = (k,v)=> v===undefined? JSON.parse(localStorage.getItem(k)||'null')
                                   : localStorage.setItem(k, JSON.stringify(v));
  const wsDefault = { labor:[], equipment:[], materials:[], contracts:[], insurance:[], other:[], meta:{} };
  const ws = Object.assign(wsDefault, ls(K) || {});
  document.getElementById('meta').textContent =
    `Category: ${ws.meta.projectCategory||'—'} · CBCS: ${(ws.meta.cbcsCodes||[]).join(', ')||'—'} · Drawings: ${(ws.meta.drawings||[]).length}`;

  const buckets = [
    ['labor','Labor'],
    ['equipment','Equipment'],
    ['materials','Materials'],
    ['contracts','Contracts'],
    ['insurance','Insurance'],
    ['other','Other']
  ];

  const wrap = document.getElementById('buckets');
  buckets.forEach(([key,label])=>{
    const card = document.createElement('div');
    card.className='card';
    card.innerHTML = `<h3>${label} <span class="pill" id="count-${key}">0</span></h3>
      <table id="tbl-${key}" data-bucket="${key}">
        <thead><tr><th>Description</th><th>Qty</th><th>Unit $</th><th>Cost Source</th><th>Reference</th><th>Total $</th><th></th></tr></thead>
        <tbody></tbody>
        <tfoot>
          <tr style="background:#f8f9fa;font-weight:bold;">
            <td colspan="5" style="text-align:right;padding:12px;">Bucket Total:</td>
            <td class="bucket-total" style="padding:12px;">$0.00</td>
            <td></td>
          </tr>
        </tfoot>
      </table>
      <button data-add="${key}">Add Row</button>`;
    wrap.appendChild(card);

    function row(r,i){
      const tr=document.createElement('tr');
      tr.innerHTML = `
        <td contenteditable class="desc">${r.desc||''}</td>
        <td><input class="qty" type="number" min="0" step="0.01" value="${r.qty??1}" style="width:100%;padding:4px;border:1px solid #ddd;border-radius:4px;"></td>
        <td><input class="rate" type="number" min="0" step="0.01" value="${r.unitCost??0}" style="width:100%;padding:4px;border:1px solid #ddd;border-radius:4px;"></td>
        <td>
          <select class="source" style="width:100%;padding:4px;border:1px solid #ddd;border-radius:4px;">
            <option value="">— Select Source —</option>
            <option value="RSMeans" ${r.costSource==='RSMeans'?'selected':''}>RSMeans (Gordian)</option>
            <option value="FEMA_EquipRates" ${r.costSource==='FEMA_EquipRates'?'selected':''}>FEMA Equipment Rates</option>
            <option value="NationalConstrEstimator" ${r.costSource==='NationalConstrEstimator'?'selected':''}>National Construction Estimator</option>
            <option value="BCIS" ${r.costSource==='BCIS'?'selected':''}>BCIS</option>
            <option value="LocalMarket" ${r.costSource==='LocalMarket'?'selected':''}>Local Market Rate</option>
            <option value="VendorQuote" ${r.costSource==='VendorQuote'?'selected':''}>Vendor Quote</option>
            <option value="Historical" ${r.costSource==='Historical'?'selected':''}>Historical Project Data</option>
            <option value="Other" ${r.costSource==='Other'?'selected':''}>Other</option>
          </select>
        </td>
        <td><input class="source-ref" placeholder="Ref (book/section/page, quote #, URL, etc.)" value="${r.sourceRef||''}" style="width:100%;padding:4px;border:1px solid #ddd;border-radius:4px;"></td>
        <td class="line-total">$0.00</td>
        <td><button class="del" data-del="${i}">✕</button></td>`;
      return tr;
    }

    const tbody = card.querySelector('tbody');
    function render(){
      tbody.innerHTML='';
      (ws[key]||[]).forEach((r,i)=> tbody.appendChild(row(r,i)));
      card.querySelector('#count-'+key).textContent = (ws[key]||[]).length;
    }
    render();

    card.addEventListener('click', (e)=>{
      const add = e.target.closest('button[data-add]');
      const del = e.target.closest('button[data-del]');
      if(add){ (ws[key]=ws[key]||[]).push({ref:'',desc:'',qty:0,unit:'',unitCost:0}); render(); }
      if(del){ const idx=Number(del.dataset.del); ws[key].splice(idx,1); render(); }
    });

    card.addEventListener('input', ()=>{
      const rows = Array.from(tbody.querySelectorAll('tr'));
      ws[key] = rows.map(tr=>{
        const desc = tr.querySelector('.desc')?.textContent?.trim() || '';
        const qty = Number(tr.querySelector('.qty')?.value || 0);
        const unitCost = Number(tr.querySelector('.rate')?.value || 0);
        const costSource = tr.querySelector('.source')?.value || '';
        const sourceRef = tr.querySelector('.source-ref')?.value || '';
        return { desc, qty, unitCost, costSource, sourceRef };
      });
      render();
    });

    card.addEventListener('change', ()=>{
      const rows = Array.from(tbody.querySelectorAll('tr'));
      ws[key] = rows.map(tr=>{
        const desc = tr.querySelector('.desc')?.textContent?.trim() || '';
        const qty = Number(tr.querySelector('.qty')?.value || 0);
        const unitCost = Number(tr.querySelector('.rate')?.value || 0);
        const costSource = tr.querySelector('.source')?.value || '';
        const sourceRef = tr.querySelector('.source-ref')?.value || '';
        return { desc, qty, unitCost, costSource, sourceRef };
      });
      render();
    });
  });

  document.getElementById('save').onclick = ()=>{ ls(K, ws); alert('Worksheet saved.'); };
  document.getElementById('export').onclick = ()=>{
    const blob=new Blob([JSON.stringify(ws,null,2)],{type:'application/json'});
    const a=Object.assign(document.createElement('a'),{href:URL.createObjectURL(blob),download:'worksheet.json'});
    document.body.appendChild(a); a.click(); URL.revokeObjectURL(a.href); a.remove();
  };
})();
</script>

<script>
(function(){
  const $ = s => document.querySelector(s);
  const $$ = s => Array.from(document.querySelectorAll(s));
  const money = n => (isFinite(n)?n:0).toLocaleString(undefined,{style:'currency',currency:'USD'});
  const num = v => parseFloat(String(v).replace(/[^0-9.\-]/g,'')) || 0;

  function rowTotal(tr){
    const q = num(tr.querySelector('.qty')?.value);
    const r = num(tr.querySelector('.rate')?.value);
    const src = tr.querySelector('.source')?.value || '';
    let total = q * r;

    // apply factors if needed
    const region = $('#cmx-region')?.value || 'default';
    const rf = (CMX_COST_FACTORS.regionFactors[region] ?? 1);
    const srcFact = CMX_COST_FACTORS.edition[src]?.factor ?? 1;
    total = total * rf * srcFact;

    tr.querySelector('.line-total').textContent = money(total);
    return total;
  }

  function summarizeProvenance(){
    const buckets = {};
    $$('.source').forEach(sel=>{
      const tr = sel.closest('tr'); if(!tr) return;
      const src = sel.value || 'Unspecified';
      const ref = tr.querySelector('.source-ref')?.value || '';
      const total = num((tr.querySelector('.line-total')?.textContent||'').replace(/[^0-9.\.]/g,'')); // parsed $
      if (!buckets[src]) buckets[src] = { total:0, lines:0, refs:new Set() };
      buckets[src].total += total;
      buckets[src].lines += 1;
      if (ref) buckets[src].refs.add(ref);
    });
    // render summary
    const box = $('#cost-source-summary');
    if (box){
      box.innerHTML = '<h4>📊 Cost Sources Summary</h4>' +
        '<ul>' + Object.entries(buckets).map(([k,v]) =>
          `<li><b>${k}</b> — ${money(v.total)} across ${v.lines} lines${v.refs.size?` (refs: ${[...v.refs].join('; ')})`:''}</li>`
        ).join('') + '</ul>';
    }
    // store snapshot on project
    try{
      const pid = localStorage.getItem('cmx:lastProjectId');
      const cmx = window.cmx;
      if (pid && cmx){
        const p = cmx.getProject(pid) || {};
        p.costSources = Object.fromEntries(Object.entries(buckets).map(([k,v]) => [k, { total:v.total, lines:v.lines, refs:[...v.refs] }]));
        p.region = $('#cmx-region')?.value || 'default';
        cmx.updateProject(pid, p);
      }
    }catch{}
  }

  function recalcAll(){
    let grand = 0;
    $$('table[data-bucket]').forEach(tbl=>{
      let sub = 0;
      $$('tbody tr', tbl).forEach(tr => sub += rowTotal(tr));
      const ft = tbl.querySelector('.bucket-total'); if (ft) ft.textContent = money(sub);
      grand += sub;
    });
    const g = document.getElementById('grand-total'); if (g) g.textContent = money(grand);
    summarizeProvenance();
  }

  document.addEventListener('input', e=>{
    if (e.target.closest('table[data-bucket]') || e.target.id==='cmx-region') recalcAll();
  });

  document.addEventListener('change', e=>{
    if (e.target.closest('table[data-bucket]') || e.target.id==='cmx-region') recalcAll();
  });

  // Evidence manifest export
  document.getElementById('export-evidence')?.addEventListener('click', ()=>{
    const pid = localStorage.getItem('cmx:lastProjectId');
    const cmx = window.cmx; const p = (cmx && cmx.getProject(pid)) || {};
    const manifest = {
      projectId: pid, when: new Date().toISOString(),
      category: p.category, region: p.region, costSources: p.costSources || {}
    };
    const j = new Blob([JSON.stringify(manifest,null,2)], {type:'application/json'});
    const m = new Blob([
      `# Cost Evidence Manifest\n\n- Project: ${pid}\n- Region: ${p.region||'default'}\n\n## Sources\n` +
      Object.entries(p.costSources||{}).map(([k,v])=>`- **${k}** — ${v.lines} lines, total ${(v.total||0).toLocaleString(undefined,{style:'currency',currency:'USD'})}${v.refs?.length?`\n  - Refs: ${v.refs.join('; ')}`:''}`).join('\n')
    ], {type:'text/markdown'});
    const save = (blob, name) => { const a = document.createElement('a'); a.href = URL.createObjectURL(blob); a.download = name; a.click(); setTimeout(()=>URL.revokeObjectURL(a.href), 1000); };
    save(j, 'cmx_evidence_manifest.json');
    save(m, 'cmx_evidence_manifest.md');
  });

  // initial
  recalcAll();
})();
</script>

<script>
window.cmx = window.cmx || {};
(function () {
  const KEY = "cmx:projects:v1";
  const actKey = id => `cmx:activity:${id}`;
  const now = () => new Date().toISOString();

  function load() { try { return JSON.parse(localStorage.getItem(KEY)) || {}; } catch { return {}; } }
  function save(all) { localStorage.setItem(KEY, JSON.stringify(all)); }

  cmx.newProject = function newProject(init) {
    const id = (init?.id) || ("PW-" + Date.now());
    const all = load();
    all[id] = {
      id,
      createdAt: now(),
      type: init?.type || "CBCS",
      category: init?.category || null,
      title: init?.title || "Untitled Project",
      applicant: init?.applicant || {},
      cbcs: { selected: [], justification: "", evidence: [] },
      uploads: [], // {name,type,size,tag}
      costs: { labor: [], equipment: [], materials: [], contracts: [], insurance: [] },
      compliance: { ehp: {}, mitigation: {}, procurement: {} },
      status: "Draft"
    };
    save(all);
    localStorage.setItem("cmx:lastProjectId", id);
    return id;
  };

  cmx.getProject = function (id) { return load()[id || localStorage.getItem("cmx:lastProjectId")] || null; };
  cmx.updateProject = function (id, patch) {
    const all = load(); if (!all[id]) return;
    all[id] = { ...all[id], ...patch };
    save(all);
  };
  cmx.push = function (id, path, item) { // e.g., path "uploads"
    const all = load(); const p = all[id]; if (!p) return;
    (p[path] ||= []).push(item); save(all);
  };
  cmx.activity = function (id, msg) {
    const arr = JSON.parse(localStorage.getItem(actKey(id)) || "[]");
    arr.unshift({ ts: now(), msg }); // newest first
    localStorage.setItem(actKey(id), JSON.stringify(arr.slice(0, 100)));
  };
})();
</script>

<!-- MAX ASSIST + AUTOPILOT INTEGRATION (SIDEWALK QUARANTINED) -->
<link rel="stylesheet" href="assist/assist.css">
<!-- <link rel="stylesheet" href="ui/sidewalk.css"> QUARANTINED: moved to legacy/sidewalk/ -->
<!-- <script src="ui/sidewalk.js" defer></script> QUARANTINED: moved to legacy/sidewalk/ -->
<script src="assist/assist.js" defer></script>
<script src="assist/autofill.js" defer></script>
</main>

<div id="cmx-footer"></div>

</body>
</html>
