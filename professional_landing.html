<!-- ARCHIVED: This file is a duplicate of landing_page.html - Archived on user request -->
<!-- DO NOT DELETE - ARCHIVED FOR REFERENCE -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARCHIVED - ComplianceMax - FEMA Compliance Made Simple</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #1f2937;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #0f172a 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                              radial-gradient(circle at 75% 75%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
        }

        /* Navigation */
        .nav {
            position: relative;
            z-index: 10;
            padding: 24px;
        }

        .nav-container {
            max-width: 1280px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-logo-text {
            font-size: 28px;
            font-weight: bold;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .nav-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .nav-btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .nav-btn.signin {
            color: white;
            border-color: rgba(255,255,255,0.2);
        }

        .nav-btn.signin:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.3);
        }

        .nav-btn.signup {
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-btn.signup:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
        }

        /* Hero Content */
        .hero-content {
            position: relative;
            z-index: 5;
            max-width: 1280px;
            margin: 0 auto;
            padding: 80px 24px;
            text-align: center;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            color: #3b82f6;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 32px;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: bold;
            color: white;
            margin-bottom: 24px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            color: #cbd5e1;
            margin-bottom: 48px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-cta {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 64px;
        }

        .cta-btn {
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 18px;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .cta-btn.primary {
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            color: white;
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }

        .cta-btn.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
        }

        .cta-btn.secondary {
            color: white;
            border-color: rgba(255,255,255,0.3);
        }

        .cta-btn.secondary:hover {
            background: rgba(255,255,255,0.1);
            border-color: rgba(255,255,255,0.5);
        }

        /* Features Section */
        .features {
            padding: 120px 24px;
            background: white;
        }

        .features-container {
            max-width: 1280px;
            margin: 0 auto;
        }

        .features-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .features-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1f2937;
        }

        .features-subtitle {
            font-size: 1.25rem;
            color: #6b7280;
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
        }

        .feature-card {
            text-align: center;
            padding: 40px 24px;
            border-radius: 16px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border-color: #3b82f6;
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 24px;
            color: white;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1f2937;
        }

        .feature-description {
            color: #6b7280;
            line-height: 1.6;
        }

        /* Pricing Section */
        .pricing {
            padding: 120px 24px;
            background: #f8fafc;
        }

        .pricing-container {
            max-width: 1280px;
            margin: 0 auto;
        }

        .pricing-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .pricing-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 16px;
            color: #1f2937;
        }

        .pricing-subtitle {
            font-size: 1.25rem;
            color: #6b7280;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 32px;
            max-width: 1000px;
            margin: 0 auto;
        }

        .pricing-card {
            background: white;
            border-radius: 16px;
            padding: 40px 32px;
            border: 2px solid #e5e7eb;
            position: relative;
            transition: all 0.3s;
        }

        .pricing-card.featured {
            border-color: #3b82f6;
            transform: scale(1.05);
        }

        .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .pricing-card.featured:hover {
            transform: scale(1.05) translateY(-8px);
        }

        .pricing-badge {
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: #3b82f6;
            color: white;
            padding: 8px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .pricing-plan {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 8px;
            color: #1f2937;
        }

        .pricing-price {
            font-size: 3rem;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 8px;
        }

        .pricing-period {
            color: #6b7280;
            margin-bottom: 32px;
        }

        .pricing-features {
            list-style: none;
            margin-bottom: 32px;
        }

        .pricing-features li {
            padding: 8px 0;
            color: #4b5563;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pricing-features li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
        }

        .pricing-cta {
            width: 100%;
            padding: 16px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .pricing-cta:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        /* CTA Section */
        .cta-section {
            padding: 120px 24px;
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            text-align: center;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .cta-title {
            font-size: 3rem;
            font-weight: bold;
            color: white;
            margin-bottom: 24px;
        }

        .cta-description {
            font-size: 1.25rem;
            color: #cbd5e1;
            margin-bottom: 48px;
        }

        .cta-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.25rem;
            }

            .hero-cta {
                flex-direction: column;
                align-items: center;
            }

            .features-title,
            .pricing-title,
            .cta-title {
                font-size: 2rem;
            }

            .nav-actions {
                gap: 8px;
            }

            .nav-btn {
                padding: 8px 16px;
                font-size: 14px;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div id="universal-nav" class="site-nav"></div>
    <main id="main" class="container">
    <!-- Hero Section -->
    <section class="hero">
        <!-- Navigation -->
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <div class="nav-logo-icon">CM</div>
                    <div class="nav-logo-text">ComplianceMax</div>
                </div>
                <div class="nav-actions">
                    <a href="signin.html" class="nav-btn signin">Sign In</a>
                    <a href="signup.html" class="nav-btn signup">Get Started</a>
                </div>
            </div>
        </nav>

        <!-- Hero Content -->
        <div class="hero-content">
            <div class="hero-badge">
                🎯 FEMA PA Compliance Automation
            </div>
            <h1 class="hero-title">
                FEMA Compliance<br>Made Simple
            </h1>
            <p class="hero-subtitle">
                Streamline your FEMA Public Assistance compliance with AI-powered document analysis, 
                automated workbook population, and expert guidance through every phase.
            </p>
            <div class="hero-cta">
                <a href="signup.html" class="cta-btn primary">Start Free Trial</a>
                <a href="#features" class="cta-btn secondary">Learn More</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="features-container">
            <div class="features-header">
                <h2 class="features-title">Powerful Features</h2>
                <p class="features-subtitle">
                    Everything you need to ensure 100% FEMA PA compliance
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3 class="feature-title">Smart Document Analysis</h3>
                    <p class="feature-description">
                        AI-powered analysis against FEMA policies with automated pod routing 
                        for specialized compliance review.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💰</div>
                    <h3 class="feature-title">Automated Workbook Population</h3>
                    <p class="feature-description">
                        Auto-populate FEMA Project Workbooks from uploaded documents 
                        with cost reasonableness validation.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3 class="feature-title">Phase 2-4 Workflow</h3>
                    <p class="feature-description">
                        Complete workflow covering Document Inventory, Content Compliance, 
                        and Reporting & Remediation phases.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Real-time Compliance</h3>
                    <p class="feature-description">
                        Instant compliance validation against master checklist with 
                        actionable recommendations and regulatory citations.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔍</div>
                    <h3 class="feature-title">Expert Guidance</h3>
                    <p class="feature-description">
                        Step-by-step wizards and expert guidance through complex 
                        FEMA requirements and procedures.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">Comprehensive Reporting</h3>
                    <p class="feature-description">
                        Detailed compliance reports with remediation workflows 
                        and audit trail documentation.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="pricing-container">
            <div class="pricing-header">
                <h2 class="pricing-title">Choose Your Plan</h2>
                <p class="pricing-subtitle">
                    Flexible pricing for organizations of all sizes
                </p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="pricing-plan">Basic Compliance</div>
                    <div class="pricing-price">$199</div>
                    <div class="pricing-period">per month</div>
                    <ul class="pricing-features">
                        <li>Document analysis & validation</li>
                        <li>Basic compliance checking</li>
                        <li>Standard reporting</li>
                        <li>Email support</li>
                        <li>Up to 50 documents/month</li>
                    </ul>
                    <button class="pricing-cta" onclick="window.location.href='signup.html'">
                        Get Started
                    </button>
                </div>

                <div class="pricing-card featured">
                    <div class="pricing-badge">Most Popular</div>
                    <div class="pricing-plan">Professional</div>
                    <div class="pricing-price">$499</div>
                    <div class="pricing-period">per month</div>
                    <ul class="pricing-features">
                        <li>Advanced AI analysis</li>
                        <li>Automated workbook population</li>
                        <li>Phase 2-4 workflow automation</li>
                        <li>Priority support</li>
                        <li>Unlimited documents</li>
                        <li>Custom compliance rules</li>
                    </ul>
                    <button class="pricing-cta" onclick="window.location.href='signup.html'">
                        Start Free Trial
                    </button>
                </div>

                <div class="pricing-card">
                    <div class="pricing-plan">Enterprise</div>
                    <div class="pricing-price">$999</div>
                    <div class="pricing-period">per month</div>
                    <ul class="pricing-features">
                        <li>Everything in Professional</li>
                        <li>Multi-user collaboration</li>
                        <li>API access</li>
                        <li>Custom integrations</li>
                        <li>Dedicated support</li>
                        <li>Training & onboarding</li>
                    </ul>
                    <button class="pricing-cta" onclick="window.location.href='signup.html'">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-container">
            <h2 class="cta-title">Ready to Streamline Your FEMA Compliance?</h2>
            <p class="cta-description">
                Join thousands of professionals who trust ComplianceMax for their FEMA PA compliance needs.
            </p>
            <div class="cta-buttons">
                <a href="signup.html" class="cta-btn primary">Start Free Trial</a>
                <a href="signin.html" class="cta-btn secondary">Sign In</a>
            </div>
        </div>
    </section>

    <script>
        // Check if user is already authenticated
        if (localStorage.getItem('isAuthenticated') === 'true') {
            // Show different CTA for authenticated users
            document.querySelectorAll('.cta-btn.primary').forEach(btn => {
                btn.textContent = 'Go to Dashboard';
                btn.href = 'dashboard.html';
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    </main>
    <div id="cmx-footer"></div>
</body>
</html>
