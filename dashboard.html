<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Wizards - ComplianceMax</title>
    <link rel="stylesheet" href="ui/nav.css">
    <link rel="stylesheet" href="ui/stack.css">
    <script src="nav.js" defer></script>
    <script src="footer.js" defer></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #253464 0%, #1e2a5a 100%);
            color: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 40px;
        }

        .logo {
            font-size: 1.8em;
            font-weight: bold;
            color: #ffc107;
        }

        .main-nav {
            display: flex;
            align-items: center;
            gap: 24px;
            flex: 1;
            justify-content: center;
        }

        .nav-item {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            white-space: nowrap;
        }

        .nav-item:hover {
            color: white;
            background: rgba(255,255,255,0.1);
        }

        .nav-item.active {
            color: #ffc107;
            background: rgba(255,193,7,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .subscription-badge {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .subscription-badge.basic {
            background: #6b7280;
        }

        .subscription-badge.professional {
            background: #667eea;
        }

        .subscription-badge.enterprise {
            background: #764ba2;
        }

        .sign-out-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sign-out-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.3);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 50px;
        }

        .welcome-title {
            font-size: 3rem;
            color: #253464;
            margin-bottom: 16px;
            font-weight: bold;
        }

        .welcome-subtitle {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 32px;
        }

        .welcome-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            margin-bottom: 40px;
        }

        .wizards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
            margin-bottom: 50px;
        }

        .wizard-card {
            background: white;
            border-radius: 20px;
            padding: 40px 32px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }

        .wizard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .wizard-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .wizard-card.featured {
            border: 3px solid #ffc107;
            transform: scale(1.05);
        }

        .wizard-card.featured::before {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            height: 8px;
        }

        .wizard-card.featured:hover {
            transform: scale(1.05) translateY(-8px);
        }

        .wizard-icon {
            font-size: 4rem;
            margin-bottom: 24px;
            display: block;
            text-align: center;
        }

        .wizard-title {
            font-size: 1.8rem;
            font-weight: bold;
            color: #253464;
            margin-bottom: 16px;
            text-align: center;
        }

        .wizard-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 24px;
            text-align: center;
        }

        .wizard-features {
            list-style: none;
            margin-bottom: 32px;
        }

        .wizard-features li {
            padding: 8px 0;
            color: #555;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .wizard-features li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .wizard-cta {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
        }

        .wizard-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .featured-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #ffc107;
            color: #253464;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .quick-actions {
            background: white;
            border-radius: 20px;
            padding: 32px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
        }

        .quick-actions h3 {
            font-size: 1.8rem;
            color: #253464;
            margin-bottom: 24px;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 12px 24px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 16px;
            }
            
            .main-nav {
                order: -1;
                width: 100%;
                justify-content: flex-start;
                overflow-x: auto;
                padding: 0 0 16px 0;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .wizards-grid {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .wizard-card.featured {
                transform: none;
            }
            
            .wizard-card.featured:hover {
                transform: translateY(-8px);
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div id="universal-nav" class="site-nav"></div>
    <main id="main" class="container">
        <!-- Header -->
        <div class="header">
        <div class="header-content">
            <div class="logo">🎯 ComplianceMax</div>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-item active">Dashboard</a>
                <a href="projects.html" class="nav-item">Projects</a>
                <a href="compliance_workflow.html" class="nav-item">Compliance Workflow</a>
                <a href="cbcs_demo.html" class="nav-item">Professional Intake</a>
                <a href="emergency_intake.html" class="nav-item">Emergency Intake</a>
                <a href="worksheet.html" class="nav-item">Worksheet</a>
                <a href="document_upload_system.html" class="nav-item">Documents</a>
            </nav>
            <div class="user-info">
                <span id="welcomeMessage">Welcome, User</span>
                <div class="user-actions">
                    <span id="subscriptionBadge" class="subscription-badge">Professional</span>
                    <button onclick="signOut()" class="sign-out-btn">Sign Out</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1 class="welcome-title">🚀 Start Your Project</h1>
            <p class="welcome-subtitle">Choose the best wizard for your Federal PA compliance needs</p>
            <div class="welcome-badge">
                🎯 Professional Tools Ready
            </div>
        </div>

        <!-- Wizards Grid -->
        <div class="wizards-grid">
            <!-- Emergency Work Intake -->
            <a href="emergency_intake.html" class="wizard-card">
                <div class="wizard-icon">🚨</div>
                <h3 class="wizard-title">Emergency Work Intake</h3>
                <p class="wizard-description">
                    Quick emergency project setup for urgent disaster response
                    and immediate compliance needs.
                </p>
                <ul class="wizard-features">
                    <li>Rapid project setup</li>
                    <li>Emergency documentation</li>
                    <li>Priority processing</li>
                    <li>Quick compliance check</li>
                </ul>
                <button class="wizard-cta">Start Emergency Work</button>
            </a>

            <!-- Permanent Work Intake -->
            <a href="permanent_work_intake.html" class="wizard-card featured">
                <div class="featured-badge">Most Common</div>
                <div class="wizard-icon">🏗️</div>
                <h3 class="wizard-title">Permanent Work Intake</h3>
                <p class="wizard-description">
                    Comprehensive permanent work project setup with detailed
                    documentation and cost analysis.
                </p>
                <ul class="wizard-features">
                    <li>Permanent work documentation</li>
                    <li>Cost reasonableness analysis</li>
                    <li>Project specifications</li>
                    <li>Workbook population</li>
                </ul>
                <button class="wizard-cta">Start Permanent Work</button>
            </a>

            <!-- Codes & Standards Intake -->
            <a href="cbcs_demo.html" class="wizard-card">
                <div class="wizard-icon">📋</div>
                <h3 class="wizard-title">Codes & Standards Intake</h3>
                <p class="wizard-description">
                    Specialized intake for codes and standards compliance
                    with CBCS code selection and analysis.
                </p>
                <ul class="wizard-features">
                    <li>CBCS code selection</li>
                    <li>Standards compliance</li>
                    <li>Code documentation</li>
                    <li>Compliance validation</li>
                </ul>
                <button class="wizard-cta">Start Codes & Standards</button>
            </a>

            <!-- Complete Compliance Workflow -->
            <a href="compliance_workflow.html" class="wizard-card">
                <div class="wizard-icon">🎯</div>
                <h3 class="wizard-title">Complete Compliance Workflow</h3>
                <p class="wizard-description">
                    Full Phase 2-4 compliance analysis with automated document processing,
                    policy matching, and comprehensive reporting.
                </p>
                <ul class="wizard-features">
                    <li>Document inventory & validation</li>
                    <li>AI-powered content analysis</li>
                    <li>Automated policy matching</li>
                    <li>Comprehensive reporting</li>
                </ul>
                <button class="wizard-cta">Start Complete Workflow</button>
            </a>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>⚡ Quick Actions</h3>
            <div class="action-buttons">
                <a href="worksheet.html" class="action-btn">📊 Open Worksheet</a>
                <a href="document_upload_system.html" class="action-btn">📄 Upload Documents</a>
                <a href="projects.html" class="action-btn">📋 View Projects</a>
                <a href="report.html" class="action-btn">📈 Generate Report</a>
            </div>
        </div>
    </div>

    <script>
        // Authentication check
        function checkAuthentication() {
            const isAuthenticated = localStorage.getItem('isAuthenticated');
            if (!isAuthenticated || isAuthenticated !== 'true') {
                window.location.href = 'landing_page.html';
                return false;
            }
            return true;
        }

        // Load user information
        function loadUserInfo() {
            const userName = localStorage.getItem('userName') || 'User';
            const userSubscription = localStorage.getItem('userSubscription') || 'professional';
            
            document.getElementById('welcomeMessage').textContent = `Welcome, ${userName}`;
            
            const subscriptionBadge = document.getElementById('subscriptionBadge');
            subscriptionBadge.textContent = userSubscription.charAt(0).toUpperCase() + userSubscription.slice(1);
            subscriptionBadge.className = `subscription-badge ${userSubscription}`;
        }

        // Sign out function
        function signOut() {
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('userEmail');
            localStorage.removeItem('userName');
            localStorage.removeItem('userOrganization');
            localStorage.removeItem('userSubscription');
            window.location.href = 'landing_page.html';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            if (!checkAuthentication()) return;
            loadUserInfo();
            console.log('✅ Start Wizards Dashboard loaded - User authenticated');
        });
    </script>
    </main>
    <div id="cmx-footer"></div>
</body>
</html>
