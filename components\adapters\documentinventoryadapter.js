/**
 * Document Inventory Adapter
 * Preserves capabilities from compliance_workflow.html Phase 2
 * Focuses on metadata extraction, reference matching, and document cataloging
 */

class DocumentInventoryAdapter {
    constructor(config) {
        this.config = config;
        this.referenceDatabase = null;
        this.loadReferenceData();
    }

    async loadReferenceData() {
        // Load FEMA reference requirements if available
        this.referenceDatabase = window.femaReferenceDatabase || this.getMockReferenceDatabase();
    }

    /**
     * Process file with focus on inventory and cataloging
     */
    async processFile(fileObj, progressCallback) {
        const steps = [
            { name: 'Extracting metadata', progress: 20, duration: 1500 },
            { name: 'Classifying document type', progress: 40, duration: 2000 },
            { name: 'Matching against references', progress: 60, duration: 2500 },
            { name: 'Cataloging document', progress: 80, duration: 1500 },
            { name: 'Generating inventory entry', progress: 100, duration: 1000 }
        ];

        let analysis = {
            type: 'document-inventory',
            metadata: null,
            classification: null,
            referenceMatches: [],
            catalogEntry: null,
            inventoryStatus: 'processed'
        };

        // Step 1: Extract metadata
        progressCallback(fileObj.id, steps[0].name, steps[0].progress);
        await this.delay(steps[0].duration);
        analysis.metadata = await this.extractMetadata(fileObj);

        // Step 2: Classify document type
        progressCallback(fileObj.id, steps[1].name, steps[1].progress);
        await this.delay(steps[1].duration);
        analysis.classification = await this.classifyDocument(fileObj, analysis.metadata);

        // Step 3: Match against references
        progressCallback(fileObj.id, steps[2].name, steps[2].progress);
        await this.delay(steps[2].duration);
        analysis.referenceMatches = await this.matchReferences(analysis.classification, analysis.metadata);

        // Step 4: Catalog document
        progressCallback(fileObj.id, steps[3].name, steps[3].progress);
        await this.delay(steps[3].duration);
        analysis.catalogEntry = await this.createCatalogEntry(fileObj, analysis);

        // Step 5: Generate inventory entry
        progressCallback(fileObj.id, steps[4].name, steps[4].progress);
        await this.delay(steps[4].duration);
        analysis.inventoryStatus = 'cataloged';

        return analysis;
    }

    /**
     * Extract comprehensive metadata from document
     */
    async extractMetadata(fileObj) {
        return {
            fileName: fileObj.name,
            fileSize: fileObj.size,
            fileType: fileObj.type,
            uploadDate: new Date().toISOString(),
            extractedProperties: {
                title: this.extractTitle(fileObj.name),
                dateCreated: null,
                author: null,
                subject: null,
                keywords: [],
                pageCount: 1,
                wordCount: 0
            },
            technicalProperties: {
                resolution: null,
                colorSpace: null,
                compression: null,
                version: null
            },
            contentAnalysis: {
                hasText: true,
                hasTables: false,
                hasImages: false,
                hasSignatures: false,
                language: 'en'
            }
        };
    }

    /**
     * Classify document based on content and metadata
     */
    async classifyDocument(fileObj, metadata) {
        const classification = {
            primaryType: 'unknown',
            secondaryType: null,
            confidence: 0.7,
            suggestedCategory: null,
            femaRelevance: 'medium'
        };

        // Analyze filename for classification clues
        const fileName = fileObj.name.toLowerCase();
        
        if (fileName.includes('estimate') || fileName.includes('cost')) {
            classification.primaryType = 'cost-estimate';
            classification.suggestedCategory = 'Financial Documentation';
            classification.femaRelevance = 'high';
        } else if (fileName.includes('drawing') || fileName.includes('plan') || fileName.includes('dwg')) {
            classification.primaryType = 'technical-drawing';
            classification.suggestedCategory = 'Technical Documentation';
            classification.femaRelevance = 'high';
        } else if (fileName.includes('photo') || fileName.includes('image') || fileObj.type.startsWith('image/')) {
            classification.primaryType = 'photographic-evidence';
            classification.suggestedCategory = 'Damage Documentation';
            classification.femaRelevance = 'high';
        } else if (fileName.includes('contract') || fileName.includes('agreement')) {
            classification.primaryType = 'contract-document';
            classification.suggestedCategory = 'Legal Documentation';
            classification.femaRelevance = 'medium';
        } else if (fileName.includes('report') || fileName.includes('analysis')) {
            classification.primaryType = 'analytical-report';
            classification.suggestedCategory = 'Technical Reports';
            classification.femaRelevance = 'high';
        } else {
            classification.primaryType = 'general-document';
            classification.suggestedCategory = 'General Documentation';
            classification.femaRelevance = 'medium';
        }

        return classification;
    }

    /**
     * Match document against FEMA reference requirements
     */
    async matchReferences(classification, metadata) {
        const matches = [];

        // Check against FEMA required documentation
        const requiredDocs = this.referenceDatabase.requiredDocuments || [];
        
        for (const reqDoc of requiredDocs) {
            const matchScore = this.calculateMatchScore(classification, metadata, reqDoc);
            
            if (matchScore > 0.5) {
                matches.push({
                    referenceId: reqDoc.id,
                    referenceName: reqDoc.name,
                    category: reqDoc.category,
                    matchScore: matchScore,
                    matchReason: this.getMatchReason(classification, reqDoc),
                    status: matchScore > 0.8 ? 'strong-match' : 'possible-match'
                });
            }
        }

        return matches.sort((a, b) => b.matchScore - a.matchScore);
    }

    /**
     * Create catalog entry for document
     */
    async createCatalogEntry(fileObj, analysis) {
        return {
            id: fileObj.id,
            fileName: fileObj.name,
            fileSize: fileObj.size,
            fileType: fileObj.type,
            uploadDate: fileObj.uploadTime,
            classification: analysis.classification,
            metadata: analysis.metadata,
            referenceMatches: analysis.referenceMatches,
            tags: this.generateTags(analysis),
            status: 'cataloged',
            lastModified: new Date().toISOString(),
            notes: this.generateNotes(analysis)
        };
    }

    /**
     * Render inventory analysis results
     */
    renderResults(file) {
        if (!file.analysis || file.analysis.type !== 'document-inventory') return '';

        const analysis = file.analysis;
        
        return `
            <div class="inventory-analysis-results">
                <div class="analysis-header">
                    <h5>📊 Document Inventory Analysis</h5>
                    <div class="inventory-status">
                        Status: ${analysis.inventoryStatus}
                    </div>
                </div>

                <!-- Document Classification -->
                <div class="analysis-section">
                    <h6>📋 Document Classification</h6>
                    <div class="classification-details">
                        <div class="detail-item">
                            <strong>Primary Type:</strong> ${analysis.classification?.primaryType || 'Unknown'}
                        </div>
                        <div class="detail-item">
                            <strong>Category:</strong> ${analysis.classification?.suggestedCategory || 'General'}
                        </div>
                        <div class="detail-item">
                            <strong>FEMA Relevance:</strong> ${analysis.classification?.femaRelevance || 'Medium'}
                        </div>
                        <div class="detail-item">
                            <strong>Confidence:</strong> ${((analysis.classification?.confidence || 0) * 100).toFixed(1)}%
                        </div>
                    </div>
                </div>

                <!-- Metadata Summary -->
                <div class="analysis-section">
                    <h6>📄 Document Metadata</h6>
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <strong>File Size:</strong> ${this.formatFileSize(analysis.metadata?.fileSize || 0)}
                        </div>
                        <div class="metadata-item">
                            <strong>Upload Date:</strong> ${new Date(analysis.metadata?.uploadDate || Date.now()).toLocaleDateString()}
                        </div>
                        <div class="metadata-item">
                            <strong>Page Count:</strong> ${analysis.metadata?.extractedProperties?.pageCount || 'N/A'}
                        </div>
                        <div class="metadata-item">
                            <strong>Has Text:</strong> ${analysis.metadata?.contentAnalysis?.hasText ? 'Yes' : 'No'}
                        </div>
                        <div class="metadata-item">
                            <strong>Has Tables:</strong> ${analysis.metadata?.contentAnalysis?.hasTables ? 'Yes' : 'No'}
                        </div>
                        <div class="metadata-item">
                            <strong>Has Images:</strong> ${analysis.metadata?.contentAnalysis?.hasImages ? 'Yes' : 'No'}
                        </div>
                    </div>
                </div>

                <!-- Reference Matches -->
                <div class="analysis-section">
                    <h6>🎯 FEMA Reference Matches</h6>
                    <div class="reference-matches">
                        ${analysis.referenceMatches?.length > 0 ? 
                            analysis.referenceMatches.map(match => `
                                <div class="reference-match ${match.status}">
                                    <div class="match-header">
                                        <strong>${match.referenceName}</strong>
                                        <span class="match-score">${(match.matchScore * 100).toFixed(0)}%</span>
                                    </div>
                                    <div class="match-details">
                                        <div class="match-category">Category: ${match.category}</div>
                                        <div class="match-reason">Reason: ${match.matchReason}</div>
                                    </div>
                                </div>
                            `).join('') : 
                            '<p>No reference matches found</p>'
                        }
                    </div>
                </div>

                <!-- Catalog Entry -->
                <div class="analysis-section">
                    <h6>📚 Catalog Entry</h6>
                    <div class="catalog-entry">
                        <div class="catalog-item">
                            <strong>Entry ID:</strong> ${analysis.catalogEntry?.id || 'N/A'}
                        </div>
                        <div class="catalog-item">
                            <strong>Tags:</strong> ${analysis.catalogEntry?.tags?.join(', ') || 'None'}
                        </div>
                        <div class="catalog-item">
                            <strong>Notes:</strong> ${analysis.catalogEntry?.notes || 'None'}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="inventoryAdapter.addToInventory('${file.id}')">
                        📚 Add to Inventory
                    </button>
                    <button class="btn btn-secondary" onclick="inventoryAdapter.generateInventoryReport('${file.id}')">
                        📊 Generate Report
                    </button>
                    <button class="btn btn-success" onclick="inventoryAdapter.exportMetadata('${file.id}')">
                        📤 Export Metadata
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Show summary results for inventory analysis
     */
    showSummaryResults(files) {
        const inventoryFiles = files.filter(f => f.analysis?.type === 'document-inventory');
        if (inventoryFiles.length === 0) return;

        const resultsContainer = document.getElementById('results-container');
        if (!resultsContainer) return;

        const totalMatches = inventoryFiles.reduce((sum, f) => sum + (f.analysis.referenceMatches?.length || 0), 0);
        const categoryCounts = {};
        
        inventoryFiles.forEach(f => {
            const category = f.analysis.classification?.suggestedCategory || 'Unknown';
            categoryCounts[category] = (categoryCounts[category] || 0) + 1;
        });

        resultsContainer.innerHTML = `
            <div class="inventory-summary-results">
                <h5>📊 Document Inventory Summary</h5>
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-value">${inventoryFiles.length}</div>
                        <div class="stat-label">Documents Cataloged</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${totalMatches}</div>
                        <div class="stat-label">Reference Matches</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${Object.keys(categoryCounts).length}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                </div>
                
                <div class="category-breakdown">
                    <h6>📋 Category Breakdown</h6>
                    ${Object.entries(categoryCounts).map(([category, count]) => `
                        <div class="category-item">
                            <span class="category-name">${category}</span>
                            <span class="category-count">${count} documents</span>
                        </div>
                    `).join('')}
                </div>
                
                <div class="summary-actions">
                    <button class="btn btn-primary" onclick="inventoryAdapter.generateMasterInventory()">
                        📚 Generate Master Inventory
                    </button>
                    <button class="btn btn-secondary" onclick="inventoryAdapter.exportInventoryData()">
                        📤 Export Inventory Data
                    </button>
                </div>
            </div>
        `;
    }

    // Action methods
    addToInventory(fileId) {
        alert('📚 Adding to Inventory\n\nDocument has been added to the master inventory database...');
    }

    generateInventoryReport(fileId) {
        alert('📊 Generating Inventory Report\n\nCreating detailed inventory report for this document...');
    }

    exportMetadata(fileId) {
        alert('📤 Exporting Metadata\n\nExporting document metadata in JSON format...');
    }

    generateMasterInventory() {
        alert('📚 Generating Master Inventory\n\nCreating comprehensive inventory report for all documents...');
    }

    exportInventoryData() {
        alert('📤 Exporting Inventory Data\n\nExporting complete inventory data in Excel format...');
    }

    // Utility methods
    extractTitle(fileName) {
        return fileName.replace(/\.[^/.]+$/, "").replace(/[-_]/g, ' ');
    }

    calculateMatchScore(classification, metadata, reqDoc) {
        let score = 0;
        
        // Type matching
        if (classification.primaryType === reqDoc.type) score += 0.5;
        if (classification.suggestedCategory === reqDoc.category) score += 0.3;
        
        // Keyword matching
        const fileName = metadata.fileName.toLowerCase();
        const reqKeywords = reqDoc.keywords || [];
        const matchedKeywords = reqKeywords.filter(keyword => fileName.includes(keyword.toLowerCase()));
        score += (matchedKeywords.length / reqKeywords.length) * 0.2;
        
        return Math.min(score, 1.0);
    }

    getMatchReason(classification, reqDoc) {
        return `Document type "${classification.primaryType}" matches requirement "${reqDoc.name}"`;
    }

    generateTags(analysis) {
        const tags = [];
        
        if (analysis.classification?.primaryType) {
            tags.push(analysis.classification.primaryType);
        }
        
        if (analysis.classification?.suggestedCategory) {
            tags.push(analysis.classification.suggestedCategory);
        }
        
        if (analysis.referenceMatches?.length > 0) {
            tags.push('fema-required');
        }
        
        return tags;
    }

    generateNotes(analysis) {
        const notes = [];
        
        if (analysis.referenceMatches?.length > 0) {
            notes.push(`Matches ${analysis.referenceMatches.length} FEMA requirements`);
        }
        
        if (analysis.classification?.confidence < 0.7) {
            notes.push('Classification confidence is low - manual review recommended');
        }
        
        return notes.join('; ');
    }

    getMockReferenceDatabase() {
        return {
            requiredDocuments: [
                {
                    id: 'req-001',
                    name: 'Cost Estimate Documentation',
                    category: 'Financial Documentation',
                    type: 'cost-estimate',
                    keywords: ['cost', 'estimate', 'budget', 'financial']
                },
                {
                    id: 'req-002',
                    name: 'Technical Drawings',
                    category: 'Technical Documentation',
                    type: 'technical-drawing',
                    keywords: ['drawing', 'plan', 'blueprint', 'schematic']
                },
                {
                    id: 'req-003',
                    name: 'Damage Photography',
                    category: 'Damage Documentation',
                    type: 'photographic-evidence',
                    keywords: ['photo', 'image', 'damage', 'before', 'after']
                }
            ]
        };
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Make available globally
window.DocumentInventoryAdapter = DocumentInventoryAdapter;
window.inventoryAdapter = new DocumentInventoryAdapter();

export { DocumentInventoryAdapter };
