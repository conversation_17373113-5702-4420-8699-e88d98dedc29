<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Document Upload System - Demo</title>
    <link rel="stylesheet" href="ui/nav.css">
    <style>
        body {
            font-family: 'Lato', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .context-selector {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .context-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .context-card:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }
        
        .context-card.active {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        
        .context-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .context-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .context-description {
            color: #6b7280;
            font-size: 0.9em;
        }
        
        .upload-container {
            min-height: 400px;
        }
        
        .capabilities-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .capability-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #3b82f6;
        }
        
        .capability-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .capability-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .capability-features li {
            color: #6b7280;
            font-size: 0.9em;
            margin-bottom: 4px;
            padding-left: 16px;
            position: relative;
        }
        
        .capability-features li:before {
            content: "✅";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="demo-header">
            <h1>🚀 Unified Document Upload System</h1>
            <p>One powerful upload system that adapts to any context while preserving all best-in-class capabilities</p>
        </div>

        <!-- Context Selection -->
        <div class="demo-section">
            <h2>📋 Select Upload Context</h2>
            <p>Choose the type of analysis you need. The upload system will adapt its capabilities accordingly.</p>
            
            <div class="context-selector">
                <div class="context-card" onclick="selectContext('fema-compliance')">
                    <div class="context-icon">🏛️</div>
                    <div class="context-title">FEMA Compliance</div>
                    <div class="context-description">
                        Advanced compliance analysis with pod system integration, form generation, and workbook creation
                    </div>
                </div>
                
                <div class="context-card" onclick="selectContext('cbcs-analysis')">
                    <div class="context-icon">🔍</div>
                    <div class="context-title">CBCS Analysis</div>
                    <div class="context-description">
                        AI-powered drawing interpretation, CBCS code suggestion, and cost takeoff generation
                    </div>
                </div>
                
                <div class="context-card" onclick="selectContext('document-inventory')">
                    <div class="context-icon">📊</div>
                    <div class="context-title">Document Inventory</div>
                    <div class="context-description">
                        Metadata extraction, reference matching, and document cataloging for compliance workflows
                    </div>
                </div>
                
                <div class="context-card" onclick="selectContext('project-documentation')">
                    <div class="context-icon">📄</div>
                    <div class="context-title">Project Documentation</div>
                    <div class="context-description">
                        General project document processing with wizard integration and data population
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Area -->
        <div class="demo-section">
            <h2 id="upload-title">📤 Document Upload</h2>
            <p id="upload-description">Select a context above to begin uploading documents.</p>
            
            <div id="upload-container" class="upload-container">
                <!-- Upload interface will be inserted here -->
            </div>
        </div>

        <!-- Capabilities Overview -->
        <div class="demo-section">
            <h2>⚡ Unified Capabilities</h2>
            <p>Every upload context includes these powerful features:</p>
            
            <div class="capabilities-list">
                <div class="capability-item">
                    <div class="capability-title">🎯 Smart Upload Interface</div>
                    <ul class="capability-features">
                        <li>Drag & drop with visual feedback</li>
                        <li>File validation and size limits</li>
                        <li>Progress tracking with detailed steps</li>
                        <li>Error handling and recovery</li>
                    </ul>
                </div>
                
                <div class="capability-item">
                    <div class="capability-title">🧠 Advanced Analysis</div>
                    <ul class="capability-features">
                        <li>Multi-engine OCR (Tesseract, EasyOCR, PaddleOCR)</li>
                        <li>AI-powered content interpretation</li>
                        <li>Context-aware processing</li>
                        <li>Metadata extraction and classification</li>
                    </ul>
                </div>
                
                <div class="capability-item">
                    <div class="capability-title">📋 FEMA Integration</div>
                    <ul class="capability-features">
                        <li>Comprehensive pod system integration</li>
                        <li>Compliance analysis and scoring</li>
                        <li>Auto-generated FEMA forms</li>
                        <li>Project workbook creation</li>
                    </ul>
                </div>
                
                <div class="capability-item">
                    <div class="capability-title">🔍 CBCS Intelligence</div>
                    <ul class="capability-features">
                        <li>AI drawing interpretation</li>
                        <li>Automatic CBCS code suggestions</li>
                        <li>Cost takeoff generation</li>
                        <li>Technical justification creation</li>
                    </ul>
                </div>
                
                <div class="capability-item">
                    <div class="capability-title">💰 Cost Analysis</div>
                    <ul class="capability-features">
                        <li>Automated cost extraction</li>
                        <li>Labor, material, equipment breakdown</li>
                        <li>Regional cost database integration</li>
                        <li>Excel workbook generation</li>
                    </ul>
                </div>
                
                <div class="capability-item">
                    <div class="capability-title">📊 Results & Actions</div>
                    <ul class="capability-features">
                        <li>Interactive results display</li>
                        <li>Context-specific actions</li>
                        <li>Export and integration options</li>
                        <li>Batch processing capabilities</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Load the unified upload system -->
    <script src="components/unified-document-upload.js"></script>
    <script src="components/adapters/femacomplianceadapter.js"></script>
    <script src="components/adapters/cbcsanalysisadapter.js"></script>
    <script src="components/adapters/documentinventoryadapter.js"></script>
    <script src="components/adapters/projectdocumentationadapter.js"></script>
    
    <script>
        let currentUpload = null;
        
        function selectContext(context) {
            // Update UI
            document.querySelectorAll('.context-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.context-card').classList.add('active');
            
            // Update titles and descriptions
            const contextInfo = {
                'fema-compliance': {
                    title: '🏛️ FEMA Compliance Upload',
                    description: 'Upload documents for comprehensive FEMA compliance analysis, pod system integration, and automatic form generation.',
                    acceptedTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png']
                },
                'cbcs-analysis': {
                    title: '🔍 CBCS Analysis Upload',
                    description: 'Upload PDF drawings and specifications for AI-powered CBCS code analysis and cost takeoff generation.',
                    acceptedTypes: ['.pdf']
                },
                'document-inventory': {
                    title: '📊 Document Inventory Upload',
                    description: 'Upload documents for metadata extraction, reference matching, and compliance inventory management.',
                    acceptedTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx']
                },
                'project-documentation': {
                    title: '📄 Project Documentation Upload',
                    description: 'Upload project documents for general processing and wizard integration.',
                    acceptedTypes: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png']
                }
            };
            
            const info = contextInfo[context];
            document.getElementById('upload-title').textContent = info.title;
            document.getElementById('upload-description').textContent = info.description;
            
            // Destroy existing upload instance
            if (currentUpload) {
                document.getElementById('upload-container').innerHTML = '';
            }
            
            // Create new upload instance with context
            currentUpload = new UnifiedDocumentUpload({
                context: context,
                containerId: 'upload-container',
                acceptedTypes: info.acceptedTypes,
                maxFileSize: 50 * 1024 * 1024, // 50MB
                maxFiles: 10,
                showProgress: true,
                enableDragDrop: true,
                enableAnalysis: true
            });
            
            console.log(`Initialized upload system for context: ${context}`);
        }
        
        // Initialize with FEMA compliance by default
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-select first context
            document.querySelector('.context-card').click();
        });
    </script>
</body>
</html>
