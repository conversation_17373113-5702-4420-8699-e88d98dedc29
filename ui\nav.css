/* Navigation positioning and layout normalization */
:root{
  --nav-h: 64px;           /* uniform nav height (match current design) */
  --sidewalk-h: 0px;       /* set to 40px on pages that use sidewalk progress */
  --z-nav: 1000;
  --z-sidewalk: 900;
}

html, body { margin: 0; padding: 0; }

/* Sticky, full-width nav at top */
.site-nav{
  position: sticky;
  top: 0;
  z-index: var(--z-nav);
  width: 100%;
  box-shadow: 0 2px 12px rgba(0,0,0,.15);
  background: rgba(37, 52, 100, 0.95);
}

.site-nav .nav-inner{
  max-width: 1200px;
  margin: 0 auto;
  height: var(--nav-h);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.site-nav .brand{ display:flex; align-items:center; gap:10px; }
.site-nav .menu{ display:flex; align-items:center; gap:18px; }

/* Center the navigation menu properly */
.site-nav nav {
  width: 100%;
  height: var(--nav-h);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}

/* Navigation link styling for accessibility */
.site-nav a {
  color: #ffffff;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  font-weight: 500;
}
.site-nav a:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffd700;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
}

/* Optional progress bar directly under nav (for pages with sidewalk) */
.sidewalk{
  position: sticky;
  top: var(--nav-h);
  z-index: var(--z-sidewalk);
  width: 100%;
  height: var(--sidewalk-h);
}

/* Content starts below nav (+ sidewalk if present) */
#main{
  padding-top: calc(var(--nav-h) + var(--sidewalk-h));
}

/* Background artwork controlled via CSS, not <img> */
.hero, .page-bg {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

/* Hide any decorative <img> used as background */
img.bg, .bg-img { display: none !important; }
