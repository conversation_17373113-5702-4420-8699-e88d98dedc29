<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Report — ComplianceMax</title>
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" href="ui/nav.css">
<link rel="stylesheet" href="ui/stack.css">
<!-- <link rel="stylesheet" href="ui/sidewalk.css"> QUARANTINED: moved to legacy/sidewalk/ -->
<link rel="stylesheet" href="print.css" media="print">

<script src="nav.js" defer></script>
<script src="footer.js" defer></script>
<style>
  body{font-family:system-ui,Segoe UI,Arial;margin:24px;max-width:1000px}
  pre{white-space:pre-wrap}
  nav a{margin-right:8px}
  .page{margin:0.5in}
  #compliance-auto{background:#f8f9fa;padding:20px;border-radius:10px;margin:20px 0}
  #compliance-auto h3{color:#253464;margin-bottom:15px}
  #compliance-auto ul{line-height:1.6}
  #compliance-auto li{margin-bottom:8px}
  @media print {
    nav, .actions { display:none !important; }
    pre{border:none}
    body{margin:0;background:#fff}
    .page{margin:0.5in}
  }
</style>
</head>
<body data-page="report" data-flow="professional" data-step="7">
<div id="universal-nav"></div>
<!-- QUARANTINED: sidewalk navigation removed -->
<!-- <div class="sidewalk-wrap"><div id="sidewalk"></div></div> -->
<!-- <script>document.documentElement.classList.add('has-sidewalk');</script> -->

<main id="main" class="container">
<div style="text-align: center; margin: 20px 0;">
  <button class="no-print" onclick="window.print()" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 20px; cursor: pointer; font-size: 16px; font-weight: 500; box-shadow: 0 2px 8px rgba(40,167,69,0.3);">
    📄 Download Comprehensive Report PDF
  </button>
</div>

<h1>Project Report (Preview)</h1>
<div class="actions" style="display:flex;gap:8px;margin:8px 0">
  <button onclick="download()">Download .md</button>
  <button onclick="window.print()">Print to PDF</button>
</div>
<pre id="md"></pre>

<script>
(function(){
  const L=(k)=>JSON.parse(localStorage.getItem(k)||'null');
  const ws = L('ComplianceMax_Demo:worksheet') || {};
  const cb = L('ComplianceMax_Demo:last_cbcs') || {};
  const pack = (k)=> (ws[k]||[]).map(r=>`| ${r.ref||''} | ${r.desc||''} | ${r.qty||0} | ${r.unit||''} | ${r.unitCost||0} | ${r.costSource||'—'} | ${(r.qty||0)*(r.unitCost||0)} |`).join('\n');

  const md = `# ComplianceMax — Technical Report

**Timestamp:** ${new Date().toISOString()}

## Project Context
- **Category:** ${ws.meta?.projectCategory || '—'}
- **CBCS Codes:** ${(ws.meta?.cbcsCodes||[]).join(', ') || '—'}
- **Drawings:** ${(ws.meta?.drawings||[]).map(d=>d.name).join(', ') || '—'}

## Technical Justification (CBCS)
${(cb.justification||'—')}

## Cost Worksheet (Summary)
### Materials
| Ref | Description | Qty | Unit | Unit $ | Cost Source | Total $ |
|---|---|---:|---|---:|---|---:|
${pack('materials') || '_(none yet)_'}
### Labor
| Ref | Description | Qty | Unit | Unit $ | Cost Source | Total $ |
|---|---|---:|---|---:|---|---:|
${pack('labor') || '_(none yet)_'}
### Equipment
| Ref | Description | Qty | Unit | Unit $ | Cost Source | Total $ |
|---|---|---:|---|---:|---|---:|
${pack('equipment') || '_(none yet)_'}
### Contracts
| Ref | Description | Qty | Unit | Unit $ | Cost Source | Total $ |
|---|---|---:|---|---:|---|---:|
${pack('contracts') || '_(none yet)_'}
### Insurance
| Ref | Description | Qty | Unit | Unit $ | Cost Source | Total $ |
|---|---|---:|---|---:|---|---:|
${pack('insurance') || '_(none yet)_'}

## Cost Source Analysis
${(() => {
  const p = cmx?.getProject(localStorage.getItem('cmx:lastProjectId'));
  const sourceTotals = p?.costsSnapshot?.sourceTotals || {};
  if (Object.keys(sourceTotals).length === 0) return '_(No cost data available)_';
  return Object.entries(sourceTotals)
    .sort(([,a], [,b]) => b - a)
    .map(([source, total]) => `- **${source}**: $${total.toLocaleString()}`)
    .join('\n');
})()}

## Compliance Analysis (Auto-Generated)

<div id="compliance-auto"></div>

## Appeals Risk Assessment
- **Similar cases reviewed:** Pattern analysis from FEMA appeals database
- **Common issues pre-mitigated:** Proactive compliance measures implemented
- **Risk score:** Based on project category, complexity, and historical data

*Generated by ComplianceMax demo.*
`;

  document.getElementById('md').textContent = md;
  window.download = function(){
    const blob=new Blob([md],{type:'text/markdown;charset=utf-8'});
    const a=Object.assign(document.createElement('a'),{href:URL.createObjectURL(blob),download:'compliance_report.md'});
    document.body.appendChild(a); a.click(); URL.revokeObjectURL(a.href); a.remove();
  };
})();
</script>

<script>
(function(){
  const pid = localStorage.getItem('cmx:lastProjectId');
  const p = (window.cmx && cmx.getProject(pid)) || {};
  const cat = p.category || '—';
  const out = [];

  // Mitigation (406)
  out.push('### 🏗️ Section 406 Mitigation Analysis');
  if ('CDEFG'.includes(cat)) {
    out.push('- Section 406 mitigation analysis required for permanent work; document options and cost-effectiveness (44 CFR 206.226).');
  }
  if (cat==='C') {
    out.push('- Bridge/road: verify scour, hydrology/hydraulics; apply ASCE 7 wind/seismic loads; consider elevation/armoring where applicable.');
  }
  if (cat==='D') {
    out.push('- Water control: ASCE 24 flood-resistant design; confirm USACE coordination where applicable.');
  }
  if (cat==='E') {
    out.push('- Buildings: IBC risk category & occupancy; special inspections (IBC Ch. 17); ASCE 7 load path.');
  }
  if (cat==='F') {
    out.push('- Utilities: NEC (NFPA 70), NFPA 110 for standby power; owner approvals and interconnection.');
  }

  // EHP (44 CFR Part 10)
  out.push('');
  out.push('### 🌿 Environmental & Historic Preservation (EHP)');
  out.push('- NEPA screening (Categorical Exclusion vs EA); attach FONSI/CE documentation when applicable.');
  out.push('- Section 106 Historic Preservation: SHPO/THPO consultation if facility ≥45 years or within a historic district.');
  out.push('- Endangered Species Act: IPaC check for listed species and critical habitat; apply timing restrictions if needed.');
  out.push('- Floodplain (EO 11988): 8-step review if in SFHA; include FIRM panel and elevation data.');
  out.push('- Wetlands (EO 11990) and CZMA: screen for wetlands/coastal zone and document consistency.');

  // Procurement (2 CFR 200)
  out.push('');
  out.push('### 📋 Procurement Compliance (2 CFR 200)');
  out.push('- Procurement review per 2 CFR 200.318-.326: method selection, competition, debarment checks, required clauses.');
  out.push('- Cost/price analysis documentation and Davis-Bacon prevailing wage requirements if applicable.');
  out.push('- Buy American Act compliance for iron, steel, and manufactured products.');

  const el = document.getElementById('compliance-auto');
  if (el){
    el.innerHTML = `<h3>🎯 Automated Compliance Analysis</h3><div style="white-space:pre-line">${
      out.join('\n')
    }</div>`;
  }
  try { cmx.activity(pid, 'Report generated with compliance auto-checks'); } catch {}
})();
</script>

<script>
// Cost Sources & Reasonableness section
(function(){
  const pid = localStorage.getItem('cmx:lastProjectId');
  const cmx = window.cmx;
  const p = (cmx && cmx.getProject(pid)) || {};
  const sources = p.costSources || {};
  const region  = p.region || 'default';

  const CMX_COST_FACTORS = {
    edition: {
      RSMeans: { year: 2025, factor: 1.00 },
      NationalConstrEstimator: { year: 2025, factor: 1.00 },
      FEMA_EquipRates: { year: 2024, factor: 1.00 }
    }
  };

  const block = document.createElement('section');
  block.className = 'page';
  block.innerHTML = `
    <h2>Cost Sources & Reasonableness</h2>
    <p><b>Region factor:</b> ${region} · <b>Edition factors:</b> RSMeans ${CMX_COST_FACTORS.edition.RSMeans?.year||'—'}, FEMA Equip ${CMX_COST_FACTORS.edition.FEMA_EquipRates?.year||'—'}</p>
    <ul>
      ${Object.entries(sources).map(([name, v]) =>
        `<li><b>${name}</b>: ${v.lines} line(s), total ${ (v.total||0).toLocaleString(undefined,{style:'currency',currency:'USD'}) }${v.refs?.length?` — refs: ${v.refs.join('; ')}`:''}</li>`
      ).join('')}
    </ul>
    <h3>Reasonableness Method</h3>
    <p>Costs validated under 2 CFR 200.404 using price analysis (triangulation). Preferred evidence: (1) RSMeans/recognized index, (2) recent vendor quotes, (3) historical project data. Where FEMA Equipment Schedule applies, rates are cross-checked against schedule and utilization documented.</p>
  `;
  document.getElementById('compliance-auto')?.after(block);
})();
</script>

<script>
window.cmx = window.cmx || {};
(function () {
  const KEY = "cmx:projects:v1";
  const actKey = id => `cmx:activity:${id}`;
  const now = () => new Date().toISOString();

  function load() { try { return JSON.parse(localStorage.getItem(KEY)) || {}; } catch { return {}; } }
  function save(all) { localStorage.setItem(KEY, JSON.stringify(all)); }

  cmx.newProject = function newProject(init) {
    const id = (init?.id) || ("PW-" + Date.now());
    const all = load();
    all[id] = {
      id,
      createdAt: now(),
      type: init?.type || "CBCS",
      category: init?.category || null,
      title: init?.title || "Untitled Project",
      applicant: init?.applicant || {},
      cbcs: { selected: [], justification: "", evidence: [] },
      uploads: [], // {name,type,size,tag}
      costs: { labor: [], equipment: [], materials: [], contracts: [], insurance: [] },
      compliance: { ehp: {}, mitigation: {}, procurement: {} },
      status: "Draft"
    };
    save(all);
    localStorage.setItem("cmx:lastProjectId", id);
    return id;
  };

  cmx.getProject = function (id) { return load()[id || localStorage.getItem("cmx:lastProjectId")] || null; };
  cmx.updateProject = function (id, patch) {
    const all = load(); if (!all[id]) return;
    all[id] = { ...all[id], ...patch };
    save(all);
  };
  cmx.push = function (id, path, item) { // e.g., path "uploads"
    const all = load(); const p = all[id]; if (!p) return;
    (p[path] ||= []).push(item); save(all);
  };
  cmx.activity = function (id, msg) {
    const arr = JSON.parse(localStorage.getItem(actKey(id)) || "[]");
    arr.unshift({ ts: now(), msg }); // newest first
    localStorage.setItem(actKey(id), JSON.stringify(arr.slice(0, 100)));
  };
})();
</script>
</main>

<div id="cmx-footer"></div>

</body>
</html>
